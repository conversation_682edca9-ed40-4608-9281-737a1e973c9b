"""
Test RTPatientSetupValidator functionality for DICOM PS3.3 C.8.8.12 RT Patient Setup Module.

Comprehensive unit tests validating all Type 1, Type 2, Type 3, Type 1C, and Type 2C
requirements including complex conditional logic, enumerated values, and sequence validation.
All tests use datasets generated from modules via to_dataset() method per the
composition-based architecture requirements.
"""
import pytest
import pydicom
from pydicom import Dataset
from pyrt_dicom.modules import RTPatientSetupModule
from pyrt_dicom.validators.modules.rt_patient_setup_validator import RTPatientSetupValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.rt_enums import (
    FixationDeviceType, ShieldingDeviceType, SetupTechnique, SetupDeviceType,
    RespiratoryMotionCompensationTechnique, RespiratorySignalSource
)
from pyrt_dicom.enums.series_enums import PatientPosition


class TestRTPatientSetupValidator:
    """Test RTPatientSetupValidator comprehensive validation functionality."""
    
    def create_valid_patient_setup_dataset(self) -> Dataset:
        """Create a valid basic RT Patient Setup module dataset for testing."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            patient_setup_label="Standard Setup"
        )
        
        patient_setup = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        return patient_setup.to_dataset()
    
    def test_validate_method_signature(self):
        """Test that validate method has correct signature and returns ValidationResult."""
        dataset = self.create_valid_patient_setup_dataset()
        config = ValidationConfig()
        
        # Test method exists and is callable
        assert hasattr(RTPatientSetupValidator, 'validate')
        assert callable(RTPatientSetupValidator.validate)
        
        # Test it accepts dataset and config parameters
        result = RTPatientSetupValidator.validate(dataset, config)
        
        # Test it returns ValidationResult
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_with_none_config(self):
        """Test validation works with None config (uses default)."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        # Should not have errors for valid dataset
        assert len(result.errors) == 0
    
    def test_valid_dataset_passes_validation(self):
        """Test that valid patient setup dataset passes validation without errors."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Type 1 Element Tests
    def test_missing_patient_setup_sequence(self):
        """Test validation fails when Type 1 Patient Setup Sequence is missing."""
        dataset = Dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient Setup Sequence (300A,0180)" in error_msg
        assert "required (Type 1)" in error_msg
    
    def test_empty_patient_setup_sequence(self):
        """Test validation fails when Patient Setup Sequence is empty."""
        dataset = Dataset()
        dataset.PatientSetupSequence = []
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient Setup Sequence (300A,0180)" in error_msg
        assert "cannot be empty" in error_msg
    
    def test_missing_patient_setup_number(self):
        """Test validation fails when Patient Setup Number is missing."""
        dataset = Dataset()
        setup_item = Dataset()
        setup_item.PatientPosition = "HFS"
        dataset.PatientSetupSequence = [setup_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient Setup Number (300A,0182)" in error_msg
        assert "required (Type 1)" in error_msg
    
    # Type 1C Conditional Requirements Tests
    def test_missing_both_patient_positions(self):
        """Test validation fails when both Patient Position and Patient Additional Position are missing."""
        dataset = Dataset()
        setup_item = Dataset()
        setup_item.PatientSetupNumber = 1
        dataset.PatientSetupSequence = [setup_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Either Patient Position (0018,5100) or Patient Additional Position (300A,0184)" in error_msg
        assert "must be present (Type 1C)" in error_msg
    
    def test_patient_position_only_passes(self):
        """Test validation passes with only Patient Position present."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_patient_additional_position_only_passes(self):
        """Test validation passes with only Patient Additional Position present."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_additional_position="Custom Position"
        )
        
        patient_setup = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        dataset = patient_setup.to_dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    # Enumerated Values Tests
    def test_invalid_patient_position_enum(self):
        """Test validation warns for invalid Patient Position enumerated value."""
        dataset = Dataset()
        setup_item = Dataset()
        setup_item.PatientSetupNumber = 1
        setup_item.PatientPosition = "INVALID_POSITION"
        dataset.PatientSetupSequence = [setup_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        # Should have warnings for invalid enum value
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Patient Position (0018,5100)" in warning_msg
    
    def test_invalid_setup_technique_enum(self):
        """Test validation warns for invalid Setup Technique enumerated value."""
        dataset = self.create_valid_patient_setup_dataset()
        dataset.PatientSetupSequence[0].SetupTechnique = "INVALID_TECHNIQUE"
        
        result = RTPatientSetupValidator.validate(dataset)
        
        # Should have warnings for invalid enum value
        assert len(result.warnings) > 0
        warning_msg = str(result.warnings[0])
        assert "Setup Technique (300A,01B0)" in warning_msg
    
    # Sequence Structure Tests
    def test_fixation_device_missing_type(self):
        """Test validation fails when Fixation Device Type is missing."""
        dataset = self.create_valid_patient_setup_dataset()
        fixation_item = Dataset()
        fixation_item.FixationDeviceLabel = "Test Device"
        dataset.PatientSetupSequence[0].FixationDeviceSequence = [fixation_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Fixation Device Type (300A,0192)" in error_msg
        assert "required (Type 1)" in error_msg
    
    def test_fixation_device_missing_label(self):
        """Test validation fails when Fixation Device Label is missing."""
        dataset = self.create_valid_patient_setup_dataset()
        fixation_item = Dataset()
        fixation_item.FixationDeviceType = "MASK"
        dataset.PatientSetupSequence[0].FixationDeviceSequence = [fixation_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Fixation Device Label (300A,0194)" in error_msg
        assert "required (Type 2)" in error_msg
    
    def test_shielding_device_missing_type(self):
        """Test validation fails when Shielding Device Type is missing."""
        dataset = self.create_valid_patient_setup_dataset()
        shielding_item = Dataset()
        shielding_item.ShieldingDeviceLabel = "Test Shield"
        dataset.PatientSetupSequence[0].ShieldingDeviceSequence = [shielding_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Shielding Device Type (300A,01A2)" in error_msg
        assert "required (Type 1)" in error_msg
    
    def test_shielding_device_missing_label(self):
        """Test validation fails when Shielding Device Label is missing."""
        dataset = self.create_valid_patient_setup_dataset()
        shielding_item = Dataset()
        shielding_item.ShieldingDeviceType = "EYE"
        dataset.PatientSetupSequence[0].ShieldingDeviceSequence = [shielding_item]
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Shielding Device Label (300A,01A4)" in error_msg
        assert "required (Type 2)" in error_msg
    
    # Patient Setup Number Uniqueness Tests
    def test_duplicate_patient_setup_numbers(self):
        """Test validation fails when Patient Setup Numbers are not unique."""
        setup_item1 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        setup_item2 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,  # Duplicate number
            patient_position=PatientPosition.HFP
        )
        
        patient_setup = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item1, setup_item2]
        )
        dataset = patient_setup.to_dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_msg = str(result.errors[0])
        assert "Patient Setup Number (1) must be unique" in error_msg
    
    def test_unique_patient_setup_numbers_pass(self):
        """Test validation passes when Patient Setup Numbers are unique."""
        setup_item1 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        setup_item2 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=2,
            patient_position=PatientPosition.HFP
        )
        
        patient_setup = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item1, setup_item2]
        )
        dataset = patient_setup.to_dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    # Configuration Tests
    def test_validation_config_conditional_requirements_disabled(self):
        """Test that conditional requirements validation can be disabled."""
        dataset = Dataset()
        setup_item = Dataset()
        setup_item.PatientSetupNumber = 1
        # Missing both position fields
        dataset.PatientSetupSequence = [setup_item]
        
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        
        result = RTPatientSetupValidator.validate(dataset, config)
        
        # Should not have conditional requirement errors
        conditional_errors = [e for e in result.errors if "Either Patient Position" in e]
        assert len(conditional_errors) == 0
    
    def test_validation_config_enumerated_values_disabled(self):
        """Test that enumerated values validation can be disabled."""
        dataset = Dataset()
        setup_item = Dataset()
        setup_item.PatientSetupNumber = 1
        setup_item.PatientPosition = "INVALID_POSITION"
        dataset.PatientSetupSequence = [setup_item]
        
        config = ValidationConfig()
        config.check_enumerated_values = False
        
        result = RTPatientSetupValidator.validate(dataset, config)
        
        # Should not have enumerated value warnings
        enum_warnings = [w for w in result.warnings if "Patient Position" in w]
        assert len(enum_warnings) == 0
    
    def test_validation_config_sequences_disabled(self):
        """Test that sequence validation can be disabled."""
        dataset = self.create_valid_patient_setup_dataset()
        fixation_item = Dataset()
        # Missing required fields
        dataset.PatientSetupSequence[0].FixationDeviceSequence = [fixation_item]
        
        config = ValidationConfig()
        config.validate_sequences = False
        
        result = RTPatientSetupValidator.validate(dataset, config)
        
        # Should not have sequence validation errors
        sequence_errors = [e for e in result.errors if "Fixation Device" in e]
        assert len(sequence_errors) == 0

    # Dataset and BaseModule Integration Tests
    def test_validate_with_dataset(self):
        """Test validator works with Dataset objects."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_with_basemodule(self):
        """Test validator works with BaseModule objects."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        result = RTPatientSetupValidator.validate(module)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    # Granular Validation Method Tests
    def test_validate_required_elements_with_dataset(self):
        """Test granular required elements validation with Dataset."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate_required_elements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_required_elements_with_basemodule(self):
        """Test granular required elements validation with BaseModule."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        result = RTPatientSetupValidator.validate_required_elements(module)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_with_dataset(self):
        """Test granular conditional requirements validation with Dataset."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate_conditional_requirements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_with_basemodule(self):
        """Test granular conditional requirements validation with BaseModule."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        result = RTPatientSetupValidator.validate_conditional_requirements(module)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_dataset(self):
        """Test granular enumerated values validation with Dataset."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate_enumerated_values(dataset)
        
        assert isinstance(result, ValidationResult)
        # May have warnings but should not have errors for valid enums

    def test_validate_enumerated_values_with_basemodule(self):
        """Test granular enumerated values validation with BaseModule."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        result = RTPatientSetupValidator.validate_enumerated_values(module)
        
        assert isinstance(result, ValidationResult)
        # May have warnings but should not have errors for valid enums

    def test_validate_sequence_structures_with_dataset(self):
        """Test granular sequence structures validation with Dataset."""
        dataset = self.create_valid_patient_setup_dataset()
        
        result = RTPatientSetupValidator.validate_sequence_structures(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_sequence_structures_with_basemodule(self):
        """Test granular sequence structures validation with BaseModule."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        result = RTPatientSetupValidator.validate_sequence_structures(module)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    # Error Condition Testing for Both Types
    def test_missing_required_elements_dataset_vs_basemodule(self):
        """Test missing required elements detection for both Dataset and BaseModule."""
        # Test with Dataset
        dataset = Dataset()
        
        result_dataset = RTPatientSetupValidator.validate_required_elements(dataset)
        assert len(result_dataset.errors) > 0
        
        # Test with BaseModule (empty sequence)
        empty_module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[]
        )
        
        result_module = RTPatientSetupValidator.validate_required_elements(empty_module)
        assert len(result_module.errors) > 0
        
        # Both should detect the missing/empty Patient Setup Sequence
        dataset_errors = ' '.join(result_dataset.errors).lower()
        module_errors = ' '.join(result_module.errors).lower()
        
        assert "patient setup sequence" in dataset_errors
        assert ("patient setup sequence" in module_errors or "empty" in module_errors)

    def test_conditional_requirements_dataset_vs_basemodule(self):
        """Test conditional requirements validation for both Dataset and BaseModule."""
        # Create setup item missing both position fields
        setup_item_data = Dataset()
        setup_item_data.PatientSetupNumber = 1
        # Missing both PatientPosition and PatientAdditionalPosition
        
        # Test with Dataset
        dataset = Dataset()
        dataset.PatientSetupSequence = [setup_item_data]
        
        result_dataset = RTPatientSetupValidator.validate_conditional_requirements(dataset)
        assert len(result_dataset.errors) > 0
        
        # Test with BaseModule - need to create manually since our factory enforces the requirement
        module = RTPatientSetupModule()
        module._dataset.PatientSetupSequence = [setup_item_data.copy()]
        
        result_module = RTPatientSetupValidator.validate_conditional_requirements(module)
        assert len(result_module.errors) > 0
        
        # Both should detect the missing conditional requirement
        dataset_errors = ' '.join(result_dataset.errors)
        module_errors = ' '.join(result_module.errors)
        
        assert "Either Patient Position" in dataset_errors or "Patient Position" in dataset_errors
        assert "Either Patient Position" in module_errors or "Patient Position" in module_errors

    def test_attribute_access_patterns(self):
        """Test that validator uses modern 'attribute in data' patterns."""
        # This test verifies the validator implementation uses modern patterns
        # by testing behavior rather than implementation details
        
        dataset = self.create_valid_patient_setup_dataset()
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[
                RTPatientSetupModule.create_patient_setup_item(
                    patient_setup_number=1,
                    patient_position=PatientPosition.HFS
                )
            ]
        )
        
        # Both should work without AttributeError or other access pattern issues
        result_dataset = RTPatientSetupValidator.validate(dataset)
        result_module = RTPatientSetupValidator.validate(module)
        
        assert isinstance(result_dataset, ValidationResult)
        assert isinstance(result_module, ValidationResult)
        
        # Both should have same validation logic outcomes
        assert len(result_dataset.errors) == 0
        assert len(result_module.errors) == 0
