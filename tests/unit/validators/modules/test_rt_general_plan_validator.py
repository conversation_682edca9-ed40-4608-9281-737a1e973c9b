"""
Test RT General Plan Module Validator functionality.

Tests for RTGeneralPlanValidator that validates DICOM PS3.3 C.8.8.9 RT General Plan Module
requirements including Type 1 elements, conditional requirements, enumerated values,
and sequence structure validation.
"""

from pydicom import Dataset

from pyrt_dicom.validators.modules.rt_general_plan_validator import RTGeneralPlanValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.rt_enums import PlanIntent, RTPlanGeometry, RTPlanRelationship
from pyrt_dicom.modules.rt_general_plan_module import RTGeneralPlanModule


class TestRTGeneralPlanValidator:
    """Test RTGeneralPlanValidator functionality."""
    
    def test_valid_patient_based_plan(self):
        """Test validation of valid patient-based plan with all required elements."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanDate = "20240101"
        dataset.RTPlanTime = "120000"
        dataset.RTPlanGeometry = "PATIENT"
        
        # Add required structure set reference for PATIENT geometry
        structure_set_item = Dataset()
        structure_set_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"
        structure_set_item.ReferencedSOPInstanceUID = "*******.*******.9"
        dataset.ReferencedStructureSetSequence = [structure_set_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_valid_treatment_device_plan(self):
        """Test validation of valid treatment device plan."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Device Plan"
        dataset.RTPlanDate = "20240101"
        dataset.RTPlanTime = "120000"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_missing_required_elements(self):
        """Test validation fails for missing Type 1 elements."""
        dataset = Dataset()
        # Missing RTPlanLabel and RTPlanGeometry
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        
        # Check for specific error messages
        error_messages = [error for error in result.errors]
        assert any("RT Plan Label (300A,0002) is required" in msg for msg in error_messages)
        assert any("RT Plan Geometry (300A,000C) is required" in msg for msg in error_messages)
    
    def test_empty_required_elements(self):
        """Test validation fails for empty Type 1 elements."""
        dataset = Dataset()
        dataset.RTPlanLabel = ""  # Empty
        dataset.RTPlanGeometry = ""  # Empty
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        
        error_messages = [error for error in result.errors]
        assert any("RT Plan Label (300A,0002) cannot be empty" in msg for msg in error_messages)
        assert any("RT Plan Geometry (300A,000C) cannot be empty" in msg for msg in error_messages)
    
    def test_conditional_structure_set_requirement(self):
        """Test Type 1C conditional requirement for structure set reference."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Patient Plan"
        dataset.RTPlanGeometry = "PATIENT"
        # Missing ReferencedStructureSetSequence
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = [error for error in result.errors]
        assert any("Referenced Structure Set Sequence (300C,0060) is required when" in msg 
                  for msg in error_messages)
    
    def test_empty_structure_set_sequence(self):
        """Test validation fails for empty structure set sequence when required."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Patient Plan"
        dataset.RTPlanGeometry = "PATIENT"
        dataset.ReferencedStructureSetSequence = []  # Empty sequence
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = [error for error in result.errors]
        assert any("Referenced Structure Set Sequence (300C,0060) cannot be empty" in msg 
                  for msg in error_messages)
    
    def test_invalid_enumerated_values(self):
        """Test validation of invalid enumerated values."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "INVALID_GEOMETRY"  # Invalid value
        dataset.PlanIntent = "INVALID_INTENT"  # Invalid value
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("RT Plan Geometry (300A,000C) value 'INVALID_GEOMETRY' is invalid" in msg 
                  for msg in error_messages)
        assert any("Plan Intent (300A,000A) value 'INVALID_INTENT' is invalid" in msg 
                  for msg in error_messages)
    
    def test_valid_enumerated_values(self):
        """Test validation passes for valid enumerated values."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = RTPlanGeometry.PATIENT.value
        dataset.PlanIntent = PlanIntent.CURATIVE.value
        
        # Add required structure set reference
        structure_set_item = Dataset()
        structure_set_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"
        structure_set_item.ReferencedSOPInstanceUID = "*******.*******.9"
        dataset.ReferencedStructureSetSequence = [structure_set_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_sequence_validation_structure_set(self):
        """Test validation of Referenced Structure Set Sequence items."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "PATIENT"
        
        # Add incomplete structure set item
        incomplete_item = Dataset()
        # Missing ReferencedSOPClassUID and ReferencedSOPInstanceUID
        dataset.ReferencedStructureSetSequence = [incomplete_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("Referenced SOP Class UID (0008,1150) is required" in msg 
                  for msg in error_messages)
        assert any("Referenced SOP Instance UID (0008,1155) is required" in msg 
                  for msg in error_messages)
    
    def test_sequence_validation_rt_plan(self):
        """Test validation of Referenced RT Plan Sequence items."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add incomplete RT plan reference
        incomplete_plan = Dataset()
        incomplete_plan.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.5"
        # Missing ReferencedSOPInstanceUID and RTPlanRelationship
        dataset.ReferencedRTPlanSequence = [incomplete_plan]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("Referenced SOP Instance UID (0008,1155) is required" in msg 
                  for msg in error_messages)
        assert any("RT Plan Relationship (300A,0055) is required" in msg 
                  for msg in error_messages)
    
    def test_invalid_rt_plan_relationship(self):
        """Test validation of invalid RT Plan Relationship values."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add RT plan reference with invalid relationship
        plan_item = Dataset()
        plan_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.5"
        plan_item.ReferencedSOPInstanceUID = "*******.*******.10"
        plan_item.RTPlanRelationship = "INVALID_RELATIONSHIP"
        dataset.ReferencedRTPlanSequence = [plan_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = [error for error in result.errors]
        assert any("RT Plan Relationship (300A,0055) value 'INVALID_RELATIONSHIP' is invalid" in msg 
                  for msg in error_messages)
    
    def test_treatment_site_code_sequence_validation(self):
        """Test validation of Treatment Site Code Sequence items."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add incomplete treatment site code
        incomplete_code = Dataset()
        incomplete_code.CodeValue = "39607008"
        # Missing CodingSchemeDesignator and CodeMeaning
        dataset.TreatmentSiteCodeSequence = [incomplete_code]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("Coding Scheme Designator (0008,0102) is required" in msg 
                  for msg in error_messages)
        assert any("Code Meaning (0008,0104) is required" in msg 
                  for msg in error_messages)
    
    def test_validation_config_options(self):
        """Test that validation configuration options work correctly."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "PATIENT"
        # Missing structure set reference (conditional requirement)
        dataset.PlanIntent = "INVALID_INTENT"  # Invalid enumerated value
        
        # Test with conditional validation disabled
        config_no_conditional = ValidationConfig(validate_conditional_requirements=False)
        result = RTGeneralPlanValidator.validate(dataset, config_no_conditional)
        
        # Should not have conditional requirement errors
        error_messages = [error for error in result.errors]
        assert not any("Referenced Structure Set Sequence" in msg for msg in error_messages)
        
        # Test with enumerated value checking disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result = RTGeneralPlanValidator.validate(dataset, config_no_enum)
        
        # Should not have enumerated value errors
        error_messages = [error for error in result.errors]
        assert not any("Plan Intent (300A,000A) value 'INVALID_INTENT' is invalid" in msg 
                      for msg in error_messages)
    
    def test_rt_assertions_sequence_warning(self):
        """Test RT Assertions Sequence generates appropriate warnings."""
        from pydicom.dataelem import DataElement
        from pydicom.valuerep import VR

        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"

        # Add incomplete assertion item using DataElement since no keyword is defined
        incomplete_assertion = Dataset()
        # Missing assertion identification elements
        rt_assertions_elem = DataElement((0x0044, 0x0110), VR.SQ, [incomplete_assertion])
        dataset[(0x0044, 0x0110)] = rt_assertions_elem

        result = RTGeneralPlanValidator.validate(dataset)

        assert len(result.warnings) >= 1
        warning_messages = [warning for warning in result.warnings]
        assert any("Should contain assertion identification elements" in msg
                  for msg in warning_messages)

    # Test granular validation methods with Dataset
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements method with Dataset."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "PATIENT"

        result = RTGeneralPlanValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

        # Test with missing required elements
        empty_dataset = Dataset()
        result = RTGeneralPlanValidator.validate_required_elements(empty_dataset)
        assert len(result.errors) >= 2

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements method with Dataset."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "PATIENT"
        # Missing structure set reference

        result = RTGeneralPlanValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        assert any("Referenced Structure Set Sequence" in error for error in result.errors)

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values method with Dataset."""
        dataset = Dataset()
        dataset.RTPlanGeometry = "INVALID_GEOMETRY"
        dataset.PlanIntent = "INVALID_INTENT"

        result = RTGeneralPlanValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 2
        assert any("RT Plan Geometry" in error for error in result.errors)
        assert any("Plan Intent" in error for error in result.errors)

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures method with Dataset."""
        dataset = Dataset()

        # Add incomplete structure set sequence
        incomplete_item = Dataset()
        dataset.ReferencedStructureSetSequence = [incomplete_item]

        result = RTGeneralPlanValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 2
        assert any("Referenced SOP Class UID" in error for error in result.errors)
        assert any("Referenced SOP Instance UID" in error for error in result.errors)

    # Test granular validation methods with BaseModule
    def test_validate_required_elements_with_basemodule(self):
        """Test validate_required_elements method with BaseModule."""
        module = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )

        result = RTGeneralPlanValidator.validate_required_elements(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

        # Test with empty module
        empty_module = RTGeneralPlanModule()
        result = RTGeneralPlanValidator.validate_required_elements(empty_module)
        assert len(result.errors) >= 2

    def test_validate_conditional_requirements_with_basemodule(self):
        """Test validate_conditional_requirements method with BaseModule."""
        module = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )
        # Missing structure set reference

        result = RTGeneralPlanValidator.validate_conditional_requirements(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        assert any("Referenced Structure Set Sequence" in error for error in result.errors)

        # Test with structure set reference added
        module.with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        result = RTGeneralPlanValidator.validate_conditional_requirements(module)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_basemodule(self):
        """Test validate_enumerated_values method with BaseModule."""
        module = RTGeneralPlanModule()
        module.RTPlanGeometry = "INVALID_GEOMETRY"
        module.PlanIntent = "INVALID_INTENT"

        result = RTGeneralPlanValidator.validate_enumerated_values(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 2
        assert any("RT Plan Geometry" in error for error in result.errors)
        assert any("Plan Intent" in error for error in result.errors)

    def test_validate_sequence_structures_with_basemodule(self):
        """Test validate_sequence_structures method with BaseModule."""
        module = RTGeneralPlanModule()

        # Add incomplete structure set sequence
        from pydicom import Dataset
        incomplete_item = Dataset()
        module.ReferencedStructureSetSequence = [incomplete_item]

        result = RTGeneralPlanValidator.validate_sequence_structures(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 2
        assert any("Referenced SOP Class UID" in error for error in result.errors)
        assert any("Referenced SOP Instance UID" in error for error in result.errors)

    def test_validator_independence_external_dataset(self):
        """Test that validator works independently with external Dataset."""
        # Create external dataset (not from module)
        external_dataset = Dataset()
        external_dataset.RTPlanLabel = "External Plan"
        external_dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        external_dataset.PlanIntent = "CURATIVE"

        result = RTGeneralPlanValidator.validate(external_dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should be valid

        # Test that all granular methods work with external dataset
        req_result = RTGeneralPlanValidator.validate_required_elements(external_dataset)
        assert len(req_result.errors) == 0

        enum_result = RTGeneralPlanValidator.validate_enumerated_values(external_dataset)
        assert len(enum_result.errors) == 0
