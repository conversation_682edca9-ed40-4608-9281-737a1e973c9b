"""
Test RT Image Module validator functionality.

Tests the RTImageValidator to ensure it correctly validates DICOM RT Image 
Module requirements according to PS3.3 C.8.8.2.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.rt_image_validator import RTImageValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.modules.rt_image_module import RTImageModule
from pyrt_dicom.enums.rt_enums import RTImagePlane
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, PixelRepresentation


class TestRTImageValidator:
    """Test RTImageValidator functionality."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns ValidationResult instance."""
        dataset = Dataset()
        result = RTImageValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_with_config(self):
        """Test validation with configuration options."""
        dataset = Dataset()
        config = ValidationConfig(
            validate_conditional_requirements=False,
            check_enumerated_values=False,
            validate_sequences=False
        )
        
        result = RTImageValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)
    
    def test_validate_required_type1_elements_missing(self):
        """Test validation fails when required Type 1 elements are missing."""
        dataset = Dataset()
        
        result = RTImageValidator.validate(dataset)
        
        # Should have errors for missing required elements
        assert len(result.errors) > 0
        
        # Check for specific required elements
        error_messages = ' '.join(result.errors)
        required_elements = [
            'Samples per Pixel',
            'Photometric Interpretation', 
            'Bits Allocated',
            'Bits Stored',
            'High Bit',
            'Pixel Representation',
            'RT Image Label',
            'Image Type',
            'RT Image Plane'
        ]
        
        for element in required_elements:
            assert element in error_messages
    
    def test_validate_required_type1_elements_present(self):
        """Test validation passes when all required Type 1 elements are present."""
        dataset = Dataset()
        
        # Add all required Type 1 elements
        dataset.SamplesPerPixel = 1
        dataset.PhotometricInterpretation = "MONOCHROME2"
        dataset.BitsAllocated = 16
        dataset.BitsStored = 16
        dataset.HighBit = 15
        dataset.PixelRepresentation = 0
        dataset.RTImageLabel = "Test Image"
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "PORTAL"]
        dataset.RTImagePlane = "NORMAL"
        dataset.ConversionType = "DI"
        
        config = ValidationConfig(
            validate_conditional_requirements=False,
            check_enumerated_values=False,
            validate_sequences=False
        )
        
        result = RTImageValidator.validate(dataset, config)
        
        # Should have no errors for required elements
        required_error_keywords = [
            'Samples per Pixel', 'Photometric Interpretation', 'Bits Allocated',
            'Bits Stored', 'High Bit', 'Pixel Representation', 'RT Image Label',
            'Image Type', 'RT Image Plane'
        ]
        
        error_messages = ' '.join(result.errors)
        for keyword in required_error_keywords:
            assert keyword not in error_messages
    
    def test_conditional_pixel_intensity_relationship_sign(self):
        """Test Type 1C validation for Pixel Intensity Relationship Sign."""
        dataset = self._create_basic_valid_dataset()
        
        # Add Pixel Intensity Relationship without Sign - should fail
        dataset.PixelIntensityRelationship = "LIN"
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Pixel Intensity Relationship Sign" in error for error in result.errors)
        assert any("required when Pixel Intensity Relationship" in error for error in result.errors)
        
        # Add the required sign - should pass
        dataset.PixelIntensityRelationshipSign = 1
        
        result = RTImageValidator.validate(dataset)
        pixel_intensity_errors = [error for error in result.errors if "Pixel Intensity Relationship Sign" in error]
        assert len(pixel_intensity_errors) == 0
    
    def test_conditional_reported_values_origin(self):
        """Test Type 2C validation for Reported Values Origin."""
        dataset = self._create_basic_valid_dataset()
        
        # Set Image Type Value 3 to PORTAL - should require Reported Values Origin
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "PORTAL"]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Reported Values Origin" in error for error in result.errors)
        assert any("PORTAL" in error for error in result.errors)
        
        # Add Reported Values Origin - should pass
        dataset.ReportedValuesOrigin = "ACTUAL"
        
        result = RTImageValidator.validate(dataset)
        reported_values_errors = [error for error in result.errors if "Reported Values Origin" in error]
        assert len(reported_values_errors) == 0
    
    def test_conditional_rt_image_orientation(self):
        """Test Type 2C validation for RT Image Orientation."""
        dataset = self._create_basic_valid_dataset()
        
        # Set RT Image Plane to NON_NORMAL - should require RT Image Orientation
        dataset.RTImagePlane = "NON_NORMAL"
        
        result = RTImageValidator.validate(dataset)
        
        assert any("RT Image Orientation" in error for error in result.errors)
        assert any("NON_NORMAL" in error for error in result.errors)
        
        # Add RT Image Orientation - should pass
        dataset.RTImageOrientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        
        result = RTImageValidator.validate(dataset)
        orientation_errors = [error for error in result.errors if "RT Image Orientation" in error]
        assert len(orientation_errors) == 0
    
    def test_conditional_patient_position(self):
        """Test Type 1C validation for Patient Position."""
        dataset = self._create_basic_valid_dataset()
        
        # Add Isocenter Position - should require Patient Position
        dataset.IsocenterPosition = [0.0, 0.0, 0.0]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Patient Position" in error for error in result.errors)
        assert any("Isocenter Position" in error for error in result.errors)
        
        # Add Patient Position - should pass
        dataset.PatientPosition = "HFS"
        
        result = RTImageValidator.validate(dataset)
        patient_position_errors = [error for error in result.errors if "Patient Position" in error]
        assert len(patient_position_errors) == 0
    
    def test_conditional_fluence_map_sequence(self):
        """Test Type 1C validation for Fluence Map Sequence."""
        dataset = self._create_basic_valid_dataset()
        
        # Set Image Type Value 3 to FLUENCE - should require Fluence Map Sequence
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "FLUENCE"]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Fluence Map Sequence" in error for error in result.errors)
        assert any("FLUENCE" in error for error in result.errors)
        
        # Add Fluence Map Sequence - should pass
        fluence_item = Dataset()
        fluence_item.FluenceDataSource = "CALCULATED"
        dataset.FluenceMapSequence = [fluence_item]
        
        result = RTImageValidator.validate(dataset)
        fluence_errors = [error for error in result.errors if "Fluence Map Sequence" in error]
        assert len(fluence_errors) == 0
    
    def test_enumerated_values_validation(self):
        """Test enumerated values are validated correctly."""
        dataset = self._create_basic_valid_dataset()
        
        # Test invalid Conversion Type
        dataset.ConversionType = "INVALID"
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Conversion Type" in warning for warning in result.warnings)
        assert any("INVALID" in warning for warning in result.warnings)
        
        # Test valid Conversion Type
        dataset.ConversionType = "DI"
        
        result = RTImageValidator.validate(dataset)
        conversion_errors = [error for error in result.errors if "Conversion Type" in error and "INVALID" in error]
        assert len(conversion_errors) == 0
    
    def test_pixel_data_consistency_validation(self):
        """Test pixel data consistency validation."""
        dataset = self._create_basic_valid_dataset()
        
        # Test High Bit consistency
        dataset.BitsStored = 16
        dataset.HighBit = 14  # Should be 15 (BitsStored - 1)
        
        result = RTImageValidator.validate(dataset)
        
        assert any("High Bit" in warning for warning in result.warnings)
        assert any("one less than Bits Stored" in warning for warning in result.warnings)
    
    def test_exposure_sequence_validation(self):
        """Test exposure sequence conditional requirements."""
        dataset = self._create_basic_valid_dataset()
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "PORTAL"]
        
        # Add exposure sequence without required KVP for PORTAL
        exposure_item = Dataset()
        dataset.ExposureSequence = [exposure_item]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("KVP" in error for error in result.errors)
        assert any("PORTAL" in error for error in result.errors)
        
        # Add required KVP
        exposure_item.KVP = 120.0
        dataset.ExposureSequence = [exposure_item]
        
        result = RTImageValidator.validate(dataset)
        kvp_errors = [error for error in result.errors if "KVP" in error and "PORTAL" in error]
        assert len(kvp_errors) == 0
    
    def test_sequence_structure_validation(self):
        """Test sequence structure validation."""
        dataset = self._create_basic_valid_dataset()
        
        # Add Referenced RT Plan Sequence without required elements
        ref_plan_item = Dataset()
        dataset.ReferencedRTPlanSequence = [ref_plan_item]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Referenced SOP Class UID" in error for error in result.errors)
        assert any("Referenced SOP Instance UID" in error for error in result.errors)
        
        # Add required elements
        ref_plan_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.5"
        ref_plan_item.ReferencedSOPInstanceUID = "*******.5"
        dataset.ReferencedRTPlanSequence = [ref_plan_item]
        
        result = RTImageValidator.validate(dataset)
        ref_plan_errors = [error for error in result.errors if "Referenced SOP" in error]
        assert len(ref_plan_errors) == 0
    
    def test_rt_image_semantics_validation(self):
        """Test RT Image specific semantic validation."""
        dataset = self._create_basic_valid_dataset()
        
        # Test SID/SAD relationship validation
        dataset.RTImageSID = 1500.0
        dataset.RadiationMachineSAD = 1000.0
        dataset.XRayImageReceptorTranslation = [0.0, 0.0, -600.0]  # Incorrect Z
        
        result = RTImageValidator.validate(dataset)
        
        # Should warn about incorrect Z coordinate (should be 1000 - 1500 = -500)
        assert any("X-Ray Image Receptor Translation Z coordinate" in warning for warning in result.warnings)
    
    def test_multi_frame_validation(self):
        """Test multi-frame specific validation requirements."""
        dataset = self._create_basic_valid_dataset()
        
        # Add multiple exposure items without Referenced Frame Number
        exposure_item1 = Dataset()
        exposure_item1.KVP = 120.0
        exposure_item2 = Dataset() 
        exposure_item2.KVP = 120.0
        
        dataset.ExposureSequence = [exposure_item1, exposure_item2]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Referenced Frame Number" in error for error in result.errors)
        assert any("more than one item" in error for error in result.errors)
        
        # Add Referenced Frame Numbers
        exposure_item1.ReferencedFrameNumber = 1
        exposure_item2.ReferencedFrameNumber = 2
        
        result = RTImageValidator.validate(dataset)
        frame_errors = [error for error in result.errors if "Referenced Frame Number" in error]
        assert len(frame_errors) == 0
    
    def test_block_sequence_validation(self):
        """Test block sequence validation."""
        dataset = self._create_basic_valid_dataset()
        
        # Add exposure with blocks
        exposure_item = Dataset()
        exposure_item.NumberOfBlocks = 1
        
        # Missing block sequence should cause error
        dataset.ExposureSequence = [exposure_item]
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Block Sequence" in error for error in result.errors)
        assert any("Number of Blocks is non-zero" in error for error in result.errors)
        
        # Add proper block sequence
        block_item = Dataset()
        block_item.BlockNumber = 1
        block_item.BlockType = "SHIELDING"
        block_item.BlockDivergence = "PRESENT"
        block_item.SourceToBlockTrayDistance = 500.0
        block_item.MaterialID = "LEAD"
        block_item.BlockNumberOfPoints = 4
        block_item.BlockData = [-50.0, -50.0, 50.0, -50.0, 50.0, 50.0, -50.0, 50.0]
        
        exposure_item.BlockSequence = [block_item]
        
        result = RTImageValidator.validate(dataset)
        block_errors = [error for error in result.errors if "Block Sequence" in error and "non-zero" in error]
        assert len(block_errors) == 0
    
    def test_enhanced_beam_limiting_device_validation(self):
        """Test Enhanced RT Beam Limiting Device validation."""
        dataset = self._create_basic_valid_dataset()
        
        # Set flag to YES without sequence
        dataset.EnhancedRTBeamLimitingDeviceDefinitionFlag = "YES"
        
        result = RTImageValidator.validate(dataset)
        
        assert any("Enhanced RT Beam Limiting Device Sequence" in error for error in result.errors)
        assert any("Flag" in error for error in result.errors)
        
        # Add required sequence
        enhanced_item = Dataset()
        dataset.EnhancedRTBeamLimitingDeviceSequence = [enhanced_item]
        
        result = RTImageValidator.validate(dataset)
        enhanced_errors = [error for error in result.errors if "Enhanced RT Beam Limiting Device Sequence" in error]
        assert len(enhanced_errors) == 0
    
    def _create_basic_valid_dataset(self) -> Dataset:
        """Create a basic valid RT Image dataset for testing."""
        dataset = Dataset()
        
        # Required Type 1 elements
        dataset.SamplesPerPixel = 1
        dataset.PhotometricInterpretation = "MONOCHROME2"
        dataset.BitsAllocated = 16
        dataset.BitsStored = 16
        dataset.HighBit = 15
        dataset.PixelRepresentation = 0
        dataset.RTImageLabel = "Test Image"
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "PORTAL"]
        dataset.RTImagePlane = "NORMAL"
        
        # Required Type 2 element
        dataset.ConversionType = "DI"
        
        return dataset

    def test_granular_validate_required_elements_with_dataset(self):
        """Test granular validate_required_elements method with Dataset."""
        dataset = Dataset()

        # Test with missing required elements
        result = RTImageValidator.validate_required_elements(dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0

        # Check for specific required elements
        error_messages = ' '.join(result.errors)
        required_elements = [
            'Samples per Pixel',
            'Photometric Interpretation',
            'Bits Allocated',
            'Bits Stored',
            'High Bit',
            'Pixel Representation',
            'RT Image Label',
            'Image Type',
            'RT Image Plane'
        ]

        for element in required_elements:
            assert element in error_messages

        # Test with all required elements present
        complete_dataset = self._create_basic_valid_dataset()
        result = RTImageValidator.validate_required_elements(complete_dataset)
        assert len(result.errors) == 0

    def test_granular_validate_required_elements_with_basemodule(self):
        """Test granular validate_required_elements method with BaseModule."""
        # Test with incomplete BaseModule
        incomplete_module = RTImageModule()
        result = RTImageValidator.validate_required_elements(incomplete_module)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0

        # Test with complete BaseModule
        complete_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        result = RTImageValidator.validate_required_elements(complete_module)
        assert len(result.errors) == 0

    def test_granular_validate_conditional_requirements_with_dataset(self):
        """Test granular validate_conditional_requirements method with Dataset."""
        dataset = self._create_basic_valid_dataset()

        # Test Pixel Intensity Relationship Sign requirement
        dataset.PixelIntensityRelationship = "LIN"
        result = RTImageValidator.validate_conditional_requirements(dataset)
        assert len(result.errors) > 0
        assert any("Pixel Intensity Relationship Sign" in error for error in result.errors)

        # Add required sign
        dataset.PixelIntensityRelationshipSign = 1
        result = RTImageValidator.validate_conditional_requirements(dataset)
        pixel_intensity_errors = [error for error in result.errors if "Pixel Intensity Relationship Sign" in error]
        assert len(pixel_intensity_errors) == 0

    def test_granular_validate_conditional_requirements_with_basemodule(self):
        """Test granular validate_conditional_requirements method with BaseModule."""
        module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NON_NORMAL  # Requires RT Image Orientation
        )

        result = RTImageValidator.validate_conditional_requirements(module)
        assert len(result.errors) > 0
        assert any("RT Image Orientation" in error for error in result.errors)

        # Add required orientation
        module.with_rt_image_orientation([1.0, 0.0, 0.0, 0.0, 1.0, 0.0])
        result = RTImageValidator.validate_conditional_requirements(module)
        orientation_errors = [error for error in result.errors if "RT Image Orientation" in error]
        assert len(orientation_errors) == 0

    def test_granular_validate_enumerated_values_with_dataset(self):
        """Test granular validate_enumerated_values method with Dataset."""
        dataset = self._create_basic_valid_dataset()

        # Test invalid Conversion Type
        dataset.ConversionType = "INVALID"
        result = RTImageValidator.validate_enumerated_values(dataset)
        assert len(result.warnings) > 0
        assert any("Conversion Type" in warning for warning in result.warnings)

        # Test valid Conversion Type
        dataset.ConversionType = "DI"
        result = RTImageValidator.validate_enumerated_values(dataset)
        conversion_warnings = [warning for warning in result.warnings if "Conversion Type" in warning and "INVALID" in warning]
        assert len(conversion_warnings) == 0

    def test_granular_validate_enumerated_values_with_basemodule(self):
        """Test granular validate_enumerated_values method with BaseModule."""
        module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )

        result = RTImageValidator.validate_enumerated_values(module)
        assert isinstance(result, ValidationResult)
        # Should have no errors for valid enumerated values
        enum_errors = [error for error in result.errors if "invalid" in error.lower()]
        assert len(enum_errors) == 0

    def test_granular_validate_sequence_structures_with_dataset(self):
        """Test granular validate_sequence_structures method with Dataset."""
        dataset = self._create_basic_valid_dataset()

        # Add Referenced RT Plan Sequence without required elements
        ref_plan_item = Dataset()
        dataset.ReferencedRTPlanSequence = [ref_plan_item]

        result = RTImageValidator.validate_sequence_structures(dataset)
        assert len(result.errors) > 0
        assert any("Referenced SOP Class UID" in error for error in result.errors)
        assert any("Referenced SOP Instance UID" in error for error in result.errors)

        # Add required elements
        ref_plan_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.5"
        ref_plan_item.ReferencedSOPInstanceUID = "*******.5"

        result = RTImageValidator.validate_sequence_structures(dataset)
        ref_plan_errors = [error for error in result.errors if "Referenced SOP" in error]
        assert len(ref_plan_errors) == 0

    def test_granular_validate_sequence_structures_with_basemodule(self):
        """Test granular validate_sequence_structures method with BaseModule."""
        module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )

        result = RTImageValidator.validate_sequence_structures(module)
        assert isinstance(result, ValidationResult)
        # Should have no errors for basic module without sequences
        assert len(result.errors) == 0

    def test_main_validate_method_orchestration_with_both_types(self):
        """Test main validate method orchestrates all granular methods with both Dataset and BaseModule."""
        # Test with Dataset
        dataset = self._create_basic_valid_dataset()
        result = RTImageValidator.validate(dataset)
        assert isinstance(result, ValidationResult)

        # Test with BaseModule
        module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        result = RTImageValidator.validate(module)
        assert isinstance(result, ValidationResult)

        # Test with validation configuration
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        result = RTImageValidator.validate(module, config)
        assert isinstance(result, ValidationResult)

        # Verify zero-copy optimization works (BaseModule passed directly)
        # This is implicitly tested by the fact that the method accepts BaseModule
        assert hasattr(module, 'RTImageLabel')  # Verify module still has its data

    def test_validator_independence_external_dataset_validation(self):
        """Test validator independence - can validate external Dataset instances."""
        # Create external dataset (not from RTImageModule)
        external_dataset = Dataset()
        external_dataset.SamplesPerPixel = 1
        external_dataset.PhotometricInterpretation = "MONOCHROME2"
        external_dataset.BitsAllocated = 16
        external_dataset.BitsStored = 16
        external_dataset.HighBit = 15
        external_dataset.PixelRepresentation = 0
        external_dataset.RTImageLabel = "External Image"
        external_dataset.ImageType = ["ORIGINAL", "PRIMARY", "PORTAL"]
        external_dataset.RTImagePlane = "NORMAL"
        external_dataset.ConversionType = "DI"

        # Validator should work independently on external datasets
        result = RTImageValidator.validate(external_dataset)
        assert isinstance(result, ValidationResult)

        # Test granular methods on external dataset
        result = RTImageValidator.validate_required_elements(external_dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # All required elements present

        result = RTImageValidator.validate_conditional_requirements(external_dataset)
        assert isinstance(result, ValidationResult)

        result = RTImageValidator.validate_enumerated_values(external_dataset)
        assert isinstance(result, ValidationResult)

        result = RTImageValidator.validate_sequence_structures(external_dataset)
        assert isinstance(result, ValidationResult)