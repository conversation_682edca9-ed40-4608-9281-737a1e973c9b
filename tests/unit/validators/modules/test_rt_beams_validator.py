"""Test RTBeamsValidator functionality.

Tests comprehensive validation of RT Beams Module according to DICOM PS3.3 C.8.8.14.
All tests use datasets generated from modules via to_dataset() method following
the new composition-based architecture patterns.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTBeamsModule
from pyrt_dicom.validators.modules.rt_beams_validator import RTBeamsValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums import BeamType, RadiationType
from pyrt_dicom.enums.rt_enums import (
    RTBeamLimitingDeviceType,
    PrimaryDosimeterUnit, 
    EnhancedRTBeamLimitingDeviceDefinitionFlag
)


class TestRTBeamsValidator:
    """Test RTBeamsValidator comprehensive validation logic."""
    
    def test_validate_method_signature(self):
        """Test validator method signature and return type."""
        # Create minimal valid dataset
        dataset = Dataset()
        dataset.BeamSequence = []
        
        # Test with default config
        result = RTBeamsValidator.validate(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test with custom config
        config = ValidationConfig()
        result = RTBeamsValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)
    
    def test_granular_validation_method_signatures(self):
        """Test granular validation method signatures and return types."""
        dataset = Dataset()
        dataset.BeamSequence = []
        
        # Test validate_required_elements
        result = RTBeamsValidator.validate_required_elements(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        
        # Test validate_conditional_requirements
        result = RTBeamsValidator.validate_conditional_requirements(dataset)
        assert isinstance(result, ValidationResult)
        
        # Test validate_enumerated_values
        result = RTBeamsValidator.validate_enumerated_values(dataset)
        assert isinstance(result, ValidationResult)
        
        # Test validate_sequence_structures
        result = RTBeamsValidator.validate_sequence_structures(dataset)
        assert isinstance(result, ValidationResult)
    
    def test_granular_validation_methods_with_basemodule(self):
        """Test granular validation methods work with BaseModule instances."""
        # Create RTBeamsModule instance for testing
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='TrueBeam',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=[
                RTBeamsModule.create_control_point_item(0, 0.0),
                RTBeamsModule.create_control_point_item(1, 1.0)
            ],
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams_module = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        
        # Test all granular methods work with BaseModule
        result = RTBeamsValidator.validate_required_elements(beams_module)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass for valid module
        
        result = RTBeamsValidator.validate_conditional_requirements(beams_module)
        assert isinstance(result, ValidationResult)
        
        result = RTBeamsValidator.validate_enumerated_values(beams_module)
        assert isinstance(result, ValidationResult)
        
        result = RTBeamsValidator.validate_sequence_structures(beams_module)
        assert isinstance(result, ValidationResult)
        
        # Test main validate method with BaseModule
        result = RTBeamsValidator.validate(beams_module)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass for valid module
    
    def test_empty_beam_sequence_validation(self):
        """Test validation of empty beam sequence."""
        dataset = Dataset()
        dataset.BeamSequence = []
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for empty beam sequence
        assert len(result.errors) > 0
        assert any('must contain one or more Items' in error for error in result.errors)
    
    def test_missing_beam_sequence_validation(self):
        """Test validation when Beam Sequence is completely missing."""
        dataset = Dataset()
        # No BeamSequence attribute
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for missing beam sequence
        assert len(result.errors) > 0
        assert any('Beam Sequence (300A,00B0) is required (Type 1)' in error for error in result.errors)
    
    def test_valid_minimal_beam_validation(self):
        """Test validation of minimally valid beam."""
        # Create minimal valid beam using module
        control_points = [
            RTBeamsModule.create_control_point_item(
                control_point_index=0,
                cumulative_meterset_weight=0.0
            ),
            RTBeamsModule.create_control_point_item(
                control_point_index=1,
                cumulative_meterset_weight=1.0
            )
        ]
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='TrueBeam',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
    
    def test_beam_type_1_element_validation(self):
        """Test validation of Type 1 (required) elements."""
        dataset = Dataset()
        beam_item = Dataset()
        # Missing required Type 1 elements
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements
        expected_missing_elements = [
            'BeamNumber', 'BeamType', 'NumberOfWedges', 'NumberOfCompensators',
            'NumberOfBoli', 'NumberOfBlocks', 'NumberOfControlPoints', 'ControlPointSequence'
        ]
        
        for element in expected_missing_elements:
            assert any(f'{element}' in error and 'is required (Type 1)' in error 
                      for error in result.errors), f'Missing error for {element}'
    
    def test_beam_type_2_element_validation(self):
        """Test validation of Type 2 (required but can be empty) elements."""
        dataset = Dataset()
        beam_item = Dataset()
        # Add Type 1 elements but missing Type 2
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = []
        # Missing TreatmentMachineName and RadiationType (Type 2)
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have errors for missing Type 2 elements
        assert any('TreatmentMachineName' in error and 'is required (Type 2)' in error 
                  for error in result.errors)
        assert any('RadiationType' in error and 'is required (Type 2)' in error 
                  for error in result.errors)
    
    def test_enhanced_device_flag_conditional_validation(self):
        """Test Enhanced RT Beam Limiting Device Definition Flag conditional logic."""
        # Test case 1: Flag absent/NO requires Beam Limiting Device Sequence
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.EnhancedRTBeamLimitingDeviceDefinitionFlag = 'NO'
        # Missing BeamLimitingDeviceSequence
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Beam Limiting Device Sequence (300A,00B6) is required when' in error 
                  for error in result.errors)
        
        # Test case 2: Flag YES requires Enhanced RT Beam Limiting Device Sequence
        beam_item.EnhancedRTBeamLimitingDeviceDefinitionFlag = 'YES'
        # Still missing EnhancedRTBeamLimitingDeviceSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Enhanced RT Beam Limiting Device Sequence (3008,00A1) is required when' in error 
                  for error in result.errors)
    
    def test_number_based_sequence_requirements(self):
        """Test number-based conditional sequence requirements."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Test wedge sequence requirement
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 2  # Non-zero requires WedgeSequence
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        # Missing WedgeSequence
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Wedge Sequence (300A,00D1) is required when' in error and 
                  'Number of Wedges (2) is non-zero' in error for error in result.errors)
        
        # Test compensator sequence requirement
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 1  # Non-zero requires CompensatorSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Compensator Sequence (300A,00E3) is required when' in error and
                  'Number of Compensators (1) is non-zero' in error for error in result.errors)
        
        # Test bolus sequence requirement
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 1  # Non-zero requires ReferencedBolusSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Referenced Bolus Sequence (300C,00B0) is required when' in error and
                  'Number of Boli (1) is non-zero' in error for error in result.errors)
        
        # Test block sequence requirement
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 1  # Non-zero requires BlockSequence
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Block Sequence (300A,00F4) is required when' in error and
                  'Number of Blocks (1) is non-zero' in error for error in result.errors)
    
    def test_final_cumulative_meterset_weight_validation(self):
        """Test Final Cumulative Meterset Weight conditional requirements."""
        control_points = [
            RTBeamsModule.create_control_point_item(0, 0.0),
            RTBeamsModule.create_control_point_item(1, 1.0)  # Non-null cumulative weight
        ]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        # Missing FinalCumulativeMetersetWeight when control points have non-null weights
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Final Cumulative Meterset Weight (300A,010E) is required when' in error and
                  'Cumulative Meterset Weight is non-null in Control Points' in error
                  for error in result.errors)
    
    def test_beam_limiting_device_mlc_validation(self):
        """Test beam limiting device MLC conditional requirements."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        
        # Create beam limiting device without leaf position boundaries for MLCX
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = 'MLCX'
        bld_item.NumberOfLeafJawPairs = 60
        # Missing LeafPositionBoundaries for MLCX
        
        beam_item.BeamLimitingDeviceSequence = [bld_item]
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Leaf Position Boundaries (300A,00BE) is required when' in error and
                  'RT Beam Limiting Device Type is MLCX' in error for error in result.errors)
    
    def test_material_id_conditional_requirements(self):
        """Test Material ID conditional requirements for compensators and blocks."""
        # Create beam with compensator that has zero-length material ID
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 1
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        
        # Compensator with zero-length Material ID should require transmission data
        comp_item = Dataset()
        comp_item.CompensatorRows = 10
        comp_item.CompensatorColumns = 10
        comp_item.MaterialID = ''  # Zero-length
        # Missing CompensatorTransmissionData
        
        beam_item.CompensatorSequence = [comp_item]
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Compensator Transmission Data (300A,00EB) is required when Material ID is zero-length' in error
                  for error in result.errors)
        
        # Test block with non-zero Material ID should require thickness data
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBlocks = 1
        beam_item.CompensatorSequence = []
        
        block_item = Dataset()
        block_item.BlockNumber = 1
        block_item.BlockType = 'SHIELDING'
        block_item.MaterialID = 'LEAD'  # Non-zero length
        # Missing BlockThickness
        
        beam_item.BlockSequence = [block_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Block Thickness (300A,0100) is required when Material ID is non-zero length' in error
                  for error in result.errors)
    
    def test_control_point_sequence_validation(self):
        """Test control point sequence validation."""
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2  # Declare 2 control points
        beam_item.BeamLimitingDeviceSequence = []
        
        # But only provide 1 control point
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        beam_item.ControlPointSequence = control_points
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Control Point Sequence contains 1 items but Number of Control Points declares 2' in error
                  for error in result.errors)
    
    def test_control_point_index_validation(self):
        """Test control point index sequential validation."""
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2
        beam_item.BeamLimitingDeviceSequence = []
        
        # Create control points with wrong indices
        cp1 = Dataset()
        cp1.ControlPointIndex = 0
        cp1.CumulativeMetersetWeight = 0.0
        
        cp2 = Dataset()
        cp2.ControlPointIndex = 5  # Should be 1, not 5
        cp2.CumulativeMetersetWeight = 1.0
        
        beam_item.ControlPointSequence = [cp1, cp2]
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Control Point Index (5) should be 1 (sequential)' in error
                  for error in result.errors)
    
    def test_beam_number_uniqueness_validation(self):
        """Test beam number uniqueness validation."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Create two beams with the same beam number
        beam1 = Dataset()
        beam1.BeamNumber = 1  # Same number
        beam1.BeamType = 'STATIC'
        beam1.TreatmentMachineName = 'Test1'
        beam1.RadiationType = 'PHOTON'
        beam1.NumberOfWedges = 0
        beam1.NumberOfCompensators = 0
        beam1.NumberOfBoli = 0
        beam1.NumberOfBlocks = 0
        beam1.NumberOfControlPoints = 1
        beam1.ControlPointSequence = control_points
        beam1.BeamLimitingDeviceSequence = []
        
        beam2 = Dataset()
        beam2.BeamNumber = 1  # Same number (should be unique)
        beam2.BeamType = 'DYNAMIC'
        beam2.TreatmentMachineName = 'Test2'
        beam2.RadiationType = 'ELECTRON'
        beam2.NumberOfWedges = 0
        beam2.NumberOfCompensators = 0
        beam2.NumberOfBoli = 0
        beam2.NumberOfBlocks = 0
        beam2.NumberOfControlPoints = 1
        beam2.ControlPointSequence = control_points
        beam2.BeamLimitingDeviceSequence = []
        
        dataset = Dataset()
        dataset.BeamSequence = [beam1, beam2]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Beam Number (1) must be unique within the RT Plan' in error
                  for error in result.errors)
    
    def test_enumerated_values_validation(self):
        """Test enumerated values validation."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'INVALID_TYPE'  # Invalid enumerated value
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'INVALID_RADIATION'  # Invalid enumerated value
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        
        dataset.BeamSequence = [beam_item]
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTBeamsValidator.validate(dataset, config)
        
        assert any('Beam Type (300A,00C4)' in warning and 'INVALID_TYPE' in warning
                  for warning in result.warnings)
        assert any('Radiation Type (300A,00C6)' in warning and 'INVALID_RADIATION' in warning
                  for warning in result.warnings)
    
    def test_validation_configuration_flags(self):
        """Test different validation configuration flags."""
        # Create dataset with various issues
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'INVALID_TYPE'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 1  # Should require WedgeSequence
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        
        dataset.BeamSequence = [beam_item]
        
        # Test with enumerated values disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = RTBeamsValidator.validate(dataset, config)
        # Should not have enumerated value errors
        assert not any('INVALID_TYPE' in error for error in result.errors)
        
        # Test with conditional requirements disabled
        config = ValidationConfig(validate_conditional_requirements=False)
        result = RTBeamsValidator.validate(dataset, config)
        # Should not have conditional requirement errors
        assert not any('Wedge Sequence (300A,00D1) is required' in error for error in result.errors)
    
    def test_final_weight_consistency_warning(self):
        """Test final cumulative meterset weight consistency warning."""
        control_points = [
            RTBeamsModule.create_control_point_item(0, 0.0),
            RTBeamsModule.create_control_point_item(1, 0.8)  # Last control point weight
        ]
        
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2
        beam_item.ControlPointSequence = control_points
        beam_item.BeamLimitingDeviceSequence = []
        beam_item.FinalCumulativeMetersetWeight = 1.0  # Different from last control point (0.8)
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate(dataset)
        
        assert any('Final Cumulative Meterset Weight (1.0) should match last control point weight (0.8)' in warning
                  for warning in result.warnings)
    
    def test_complex_beam_validation_success(self):
        """Test validation of complex beam with all elements passes."""
        # Create a complex beam with wedges, compensators, blocks, etc.
        control_points = [
            RTBeamsModule.create_control_point_item(0, 0.0),
            RTBeamsModule.create_control_point_item(1, 1.0)
        ]
        
        wedge_item = RTBeamsModule.create_wedge_item(
            wedge_number=1,
            wedge_type='STANDARD',
            wedge_angle=15.0,
            wedge_factor=0.75,
            wedge_orientation=90.0
        )
        
        compensator_item = RTBeamsModule.create_compensator_item(
            compensator_number=1,
            material_id='LEAD',  # Non-zero, requires thickness data
            compensator_rows=10,
            compensator_columns=10,
            compensator_thickness_data=[1.0, 2.0, 3.0]  # Required for non-zero material ID
        )
        
        block_item = RTBeamsModule.create_block_item(
            block_number=1,
            block_type='SHIELDING',
            block_divergence='PRESENT',
            material_id='',  # Zero-length, requires transmission data
            block_number_of_points=4,
            block_data=[-10.0, -10.0, 10.0, -10.0, 10.0, 10.0, -10.0, 10.0],
            block_transmission=0.05  # Required for zero-length material ID
        )
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='TrueBeam',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=1,
            number_of_compensators=1,
            number_of_boli=0,
            number_of_blocks=1,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ],
            wedge_sequence=[wedge_item],
            compensator_sequence=[compensator_item],
            block_sequence=[block_item]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Complex but valid beam should pass validation
        assert len(result.errors) == 0, f'Unexpected validation errors: {result.errors}'
    
    def test_beam_creation_validation_scenarios(self):
        """Test validation scenarios moved from module create_beam_item method."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Test beam creation with missing beam limiting device sequence when flag is NO
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='Test',
            radiation_type=RadiationType.PHOTON,
            enhanced_rt_beam_limiting_device_definition_flag='NO'
            # Missing beam_limiting_device_sequence - should be caught by validator
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for missing beam limiting device sequence
        assert any('Beam Limiting Device Sequence (300A,00B6) is required when' in error
                  for error in result.errors)
    
    def test_beam_limiting_device_creation_validation_scenarios(self):
        """Test validation scenarios moved from module create_beam_limiting_device_item method."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Create beam with MLCX device missing leaf position boundaries
        bld_item = RTBeamsModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.MLCX,
            number_of_leaf_jaw_pairs=60
            # Missing leaf_position_boundaries - should be caught by validator
        )
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='Test',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=1,
            control_point_sequence=control_points,
            beam_limiting_device_sequence=[bld_item]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for missing leaf position boundaries for MLCX
        assert any('Leaf Position Boundaries (300A,00BE) is required when' in error and 'MLCX' in error
                  for error in result.errors)
    
    def test_compensator_creation_validation_scenarios(self):
        """Test validation scenarios moved from module create_compensator_item method."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Create compensator with zero-length material ID missing transmission data
        comp_item = RTBeamsModule.create_compensator_item(
            compensator_number=1,
            material_id='',  # Zero-length - should require transmission data
            compensator_rows=10,
            compensator_columns=10
            # Missing compensator_transmission_data - should be caught by validator
        )
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='Test',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=1,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=1,
            control_point_sequence=control_points,
            beam_limiting_device_sequence=[],
            compensator_sequence=[comp_item]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for missing transmission data when material ID is zero-length
        assert any('Compensator Transmission Data (300A,00EB) is required when Material ID is zero-length' in error
                  for error in result.errors)
    
    def test_block_creation_validation_scenarios(self):
        """Test validation scenarios moved from module create_block_item method."""
        control_points = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        # Create block with non-zero material ID missing thickness data
        block_item = RTBeamsModule.create_block_item(
            block_number=1,
            block_type='SHIELDING',
            block_divergence='PRESENT',
            material_id='LEAD',  # Non-zero length - should require thickness data
            block_number_of_points=4,
            block_data=[-10.0, -10.0, 10.0, -10.0, 10.0, 10.0, -10.0, 10.0]
            # Missing block_thickness - should be caught by validator
        )
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='Test',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=1,
            number_of_control_points=1,
            control_point_sequence=control_points,
            beam_limiting_device_sequence=[],
            block_sequence=[block_item]
        )
        
        beams = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams.to_dataset()
        
        result = RTBeamsValidator.validate(dataset)
        
        # Should have error for missing thickness data when material ID is non-zero length
        assert any('Block Thickness (300A,0100) is required when Material ID is non-zero length' in error
                  for error in result.errors)
    
    def test_granular_validate_required_elements_with_dataset(self):
        """Test validate_required_elements granular method with Dataset."""
        dataset = Dataset()
        dataset.BeamSequence = []
        
        result = RTBeamsValidator.validate_required_elements(dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any('must contain one or more Items' in error for error in result.errors)
        
        # Test with missing beam sequence entirely
        empty_dataset = Dataset()
        result = RTBeamsValidator.validate_required_elements(empty_dataset)
        assert len(result.errors) > 0
        assert any('Beam Sequence (300A,00B0) is required (Type 1)' in error for error in result.errors)
    
    def test_granular_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements granular method with Dataset."""
        # Create dataset with conditional requirement violations
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 1  # Non-zero requires WedgeSequence
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = [RTBeamsModule.create_control_point_item(0, 0.0)]
        beam_item.BeamLimitingDeviceSequence = []
        # Missing WedgeSequence for non-zero NumberOfWedges
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate_conditional_requirements(dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any('Wedge Sequence (300A,00D1) is required when' in error for error in result.errors)
    
    def test_granular_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values granular method with Dataset."""
        # Create dataset with invalid enumerated values
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'INVALID_TYPE'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'INVALID_RADIATION'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        beam_item.ControlPointSequence = [RTBeamsModule.create_control_point_item(0, 0.0)]
        beam_item.BeamLimitingDeviceSequence = []
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate_enumerated_values(dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0  # Enumerated values generate warnings
        assert any('Beam Type (300A,00C4)' in warning and 'INVALID_TYPE' in warning for warning in result.warnings)
        assert any('Radiation Type (300A,00C6)' in warning and 'INVALID_RADIATION' in warning for warning in result.warnings)
    
    def test_granular_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures granular method with Dataset."""
        # Create dataset with sequence structure violations
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2  # Declare 2 control points
        beam_item.BeamLimitingDeviceSequence = []
        
        # But only provide 1 control point (mismatch)
        beam_item.ControlPointSequence = [RTBeamsModule.create_control_point_item(0, 0.0)]
        
        dataset.BeamSequence = [beam_item]
        
        result = RTBeamsValidator.validate_sequence_structures(dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any('Control Point Sequence contains 1 items but Number of Control Points declares 2' in error for error in result.errors)
    
    def test_granular_validation_methods_independence(self):
        """Test that granular validation methods work independently."""
        # Create dataset with multiple issue types
        dataset = Dataset()
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'INVALID_TYPE'  # Enumerated value issue
        beam_item.TreatmentMachineName = 'Test'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 1  # Conditional requirement issue (missing WedgeSequence)
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 2  # Sequence structure issue (mismatch with actual count)
        beam_item.ControlPointSequence = [RTBeamsModule.create_control_point_item(0, 0.0)]  # Only 1, not 2
        beam_item.BeamLimitingDeviceSequence = []
        
        dataset.BeamSequence = [beam_item]
        
        # Each method should only report issues in its domain
        required_result = RTBeamsValidator.validate_required_elements(dataset)
        assert len(required_result.errors) == 0  # No required element issues
        
        conditional_result = RTBeamsValidator.validate_conditional_requirements(dataset)
        assert len(conditional_result.errors) > 0  # Has conditional issues
        assert any('Wedge Sequence' in error for error in conditional_result.errors)
        assert not any('INVALID_TYPE' in error for error in conditional_result.errors)  # No enum issues
        
        enum_result = RTBeamsValidator.validate_enumerated_values(dataset)
        assert len(enum_result.warnings) > 0  # Has enum issues
        assert any('INVALID_TYPE' in warning for warning in enum_result.warnings)
        assert not any('Wedge Sequence' in warning for warning in enum_result.warnings)  # No conditional issues
        
        structure_result = RTBeamsValidator.validate_sequence_structures(dataset)
        assert len(structure_result.errors) > 0  # Has structure issues
        assert any('Control Point Sequence contains 1 items but Number of Control Points declares 2' in error for error in structure_result.errors)
        assert not any('INVALID_TYPE' in error for error in structure_result.errors)  # No enum issues
    
    def test_external_dataset_validation_independence(self):
        """Test that validator works independently with external Dataset objects."""
        # Create Dataset directly without using any modules
        external_dataset = Dataset()
        
        # Minimal valid beam data created without module helpers
        beam_item = Dataset()
        beam_item.BeamNumber = 1
        beam_item.BeamType = 'STATIC'
        beam_item.TreatmentMachineName = 'External'
        beam_item.RadiationType = 'PHOTON'
        beam_item.NumberOfWedges = 0
        beam_item.NumberOfCompensators = 0
        beam_item.NumberOfBoli = 0
        beam_item.NumberOfBlocks = 0
        beam_item.NumberOfControlPoints = 1
        
        # Create control point without module helper
        cp_item = Dataset()
        cp_item.ControlPointIndex = 0
        cp_item.CumulativeMetersetWeight = None  # Use None to avoid Final Cumulative requirement
        beam_item.ControlPointSequence = [cp_item]
        
        # Create beam limiting device without module helper
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = 'X'
        bld_item.NumberOfLeafJawPairs = 1
        beam_item.BeamLimitingDeviceSequence = [bld_item]
        
        external_dataset.BeamSequence = [beam_item]
        
        # All validator methods should work with external Dataset
        result = RTBeamsValidator.validate(external_dataset)
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Valid external dataset should pass
        
        # Granular methods should also work
        required_result = RTBeamsValidator.validate_required_elements(external_dataset)
        assert isinstance(required_result, ValidationResult)
        assert len(required_result.errors) == 0
        
        conditional_result = RTBeamsValidator.validate_conditional_requirements(external_dataset)
        assert isinstance(conditional_result, ValidationResult)
        assert len(conditional_result.errors) == 0
        
        enum_result = RTBeamsValidator.validate_enumerated_values(external_dataset)
        assert isinstance(enum_result, ValidationResult)
        
        structure_result = RTBeamsValidator.validate_sequence_structures(external_dataset)
        assert isinstance(structure_result, ValidationResult)
        assert len(structure_result.errors) == 0
    
    def test_basemodule_vs_dataset_consistency(self):
        """Test that validator produces consistent results for BaseModule vs Dataset."""
        # Create RTBeamsModule instance
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name='TrueBeam',
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=1,
            control_point_sequence=[RTBeamsModule.create_control_point_item(0, 0.0)],
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams_module = RTBeamsModule.from_required_elements(beam_sequence=[beam_item])
        dataset = beams_module.to_dataset()
        
        # Validate both BaseModule and Dataset
        module_result = RTBeamsValidator.validate(beams_module)
        dataset_result = RTBeamsValidator.validate(dataset)
        
        # Results should be consistent
        assert len(module_result.errors) == len(dataset_result.errors)
        assert len(module_result.warnings) == len(dataset_result.warnings)
        assert module_result.is_valid == dataset_result.is_valid
        
        # Test granular methods too
        module_req_result = RTBeamsValidator.validate_required_elements(beams_module)
        dataset_req_result = RTBeamsValidator.validate_required_elements(dataset)
        
        assert len(module_req_result.errors) == len(dataset_req_result.errors)
        assert module_req_result.is_valid == dataset_req_result.is_valid
        
        module_cond_result = RTBeamsValidator.validate_conditional_requirements(beams_module)
        dataset_cond_result = RTBeamsValidator.validate_conditional_requirements(dataset)
        
        assert len(module_cond_result.errors) == len(dataset_cond_result.errors)
        assert module_cond_result.is_valid == dataset_cond_result.is_valid