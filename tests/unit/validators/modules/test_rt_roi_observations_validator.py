"""
Test RT ROI Observations Module validator functionality.

Tests the RTROIObservationsValidator to ensure it correctly validates DICOM RT ROI 
Observations Module requirements according to PS3.3 C.8.8.8.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.rt_roi_observations_validator import RTROIObservationsValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.modules.rt_roi_observations_module import RTROIObservationsModule
from pyrt_dicom.enums.rt_enums import RTROIInterpretedType, RTROIRelationship, ROIPhysicalProperty


class TestRTROIObservationsValidator:
    """Test RTROIObservationsValidator comprehensive validation logic."""

    def test_validate_method_signature(self):
        """Test validator method signature and return type."""
        # Create minimal valid dataset
        dataset = Dataset()

        # Test with default config
        result = RTROIObservationsValidator.validate(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)

        # Test with custom config
        config = ValidationConfig()
        result = RTROIObservationsValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)

    def test_validate_with_basemodule(self):
        """Test validator works with BaseModule instances (zero-copy validation)."""
        # Create module instance
        module = RTROIObservationsModule.from_required_elements()

        # Test main validate method with BaseModule
        result = RTROIObservationsValidator.validate(module)
        assert isinstance(result, ValidationResult)

        # Test granular methods with BaseModule
        result = RTROIObservationsValidator.validate_required_elements(module)
        assert isinstance(result, ValidationResult)

        result = RTROIObservationsValidator.validate_conditional_requirements(module)
        assert isinstance(result, ValidationResult)

        result = RTROIObservationsValidator.validate_enumerated_values(module)
        assert isinstance(result, ValidationResult)

        result = RTROIObservationsValidator.validate_sequence_structures(module)
        assert isinstance(result, ValidationResult)

        result = RTROIObservationsValidator.validate_semantic_constraints(module)
        assert isinstance(result, ValidationResult)
    
    def test_validate_empty_dataset_passes(self):
        """Test that empty dataset passes validation (no required elements at top level)."""
        dataset = Dataset()
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert result.is_valid
    
    def test_validate_valid_dataset_from_module_passes(self):
        """Test that valid dataset from RTROIObservationsModule passes validation."""
        module = RTROIObservationsModule.from_required_elements()

        # Add valid observation with non-therapeutic type to avoid warnings
        obs_item = RTROIObservationsModule.create_rt_roi_observations_item(
            observation_number=1,
            referenced_roi_number=1,
            rt_roi_interpreted_type=RTROIInterpretedType.EXTERNAL,  # Non-therapeutic type
            roi_interpreter="Dr. Smith"
        )

        module.with_optional_elements(rt_roi_observations_sequence=[obs_item])

        # CRITICAL: Use dataset from module, not module directly
        dataset = module.to_dataset()
        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert result.is_valid
    
    def test_validate_missing_type1_elements_fails(self):
        """Test that missing Type 1 elements in sequence items fail validation."""
        dataset = Dataset()
        
        # Create incomplete observation item missing required elements
        incomplete_obs = Dataset()
        # Missing ObservationNumber (Type 1)
        # Missing ReferencedROINumber (Type 1)
        incomplete_obs.RTROIInterpretedType = "PTV"
        incomplete_obs.ROIInterpreter = "Dr. Test"
        
        dataset.RTROIObservationsSequence = [incomplete_obs]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) >= 2  # Should have errors for missing Type 1 elements
        assert any("Observation Number (3006,0082) is required" in error for error in result.errors)
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)
    
    def test_validate_missing_type2_elements_fails(self):
        """Test that missing Type 2 elements in sequence items fail validation."""
        dataset = Dataset()
        
        # Create observation item missing Type 2 elements
        incomplete_obs = Dataset()
        incomplete_obs.ObservationNumber = 1
        incomplete_obs.ReferencedROINumber = 1
        # Missing RTROIInterpretedType (Type 2)
        # Missing ROIInterpreter (Type 2)
        
        dataset.RTROIObservationsSequence = [incomplete_obs]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) >= 2  # Should have errors for missing Type 2 elements
        assert any("RT ROI Interpreted Type (3006,00A4) is required" in error for error in result.errors)
        assert any("ROI Interpreter (3006,00A6) is required" in error for error in result.errors)
    
    def test_validate_invalid_enumerated_values_fails(self):
        """Test that invalid enumerated values fail validation."""
        dataset = Dataset()
        
        # Create observation with invalid enumerated values
        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "INVALID_TYPE"  # Invalid enumerated value
        obs_item.ROIInterpreter = "Dr. Test"
        
        dataset.RTROIObservationsSequence = [obs_item]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        assert any("RT ROI Interpreted Type (3006,00A4) value 'INVALID_TYPE' is not valid" in error for error in result.errors)
    
    def test_validate_conditional_requirements_elem_fraction(self):
        """Test Type 1C conditional requirement for elemental composition."""
        dataset = Dataset()
        
        # Create observation with physical properties requiring elemental composition
        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"
        
        # Physical property with ELEM_FRACTION but missing required sequence
        phys_props = Dataset()
        phys_props.ROIPhysicalProperty = "ELEM_FRACTION"
        phys_props.ROIPhysicalPropertyValue = 1.0
        # Missing ROIElementalCompositionSequence (Type 1C - required for ELEM_FRACTION)
        
        obs_item.ROIPhysicalPropertiesSequence = [phys_props]
        dataset.RTROIObservationsSequence = [obs_item]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        assert any("ROI Elemental Composition Sequence (3006,00B6) is required when" in error for error in result.errors)
    
    def test_validate_observation_number_uniqueness(self):
        """Test that duplicate observation numbers fail validation."""
        dataset = Dataset()
        
        # Create two observations with same observation number
        obs1 = Dataset()
        obs1.ObservationNumber = 1  # Duplicate
        obs1.ReferencedROINumber = 1
        obs1.RTROIInterpretedType = "PTV"
        obs1.ROIInterpreter = "Dr. Test1"
        
        obs2 = Dataset()
        obs2.ObservationNumber = 1  # Duplicate
        obs2.ReferencedROINumber = 2
        obs2.RTROIInterpretedType = "CTV"
        obs2.ROIInterpreter = "Dr. Test2"
        
        dataset.RTROIObservationsSequence = [obs1, obs2]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        assert any("Observation Number (1) must be unique" in error for error in result.errors)
    
    def test_validate_physical_properties_consistency(self):
        """Test validation of physical properties consistency."""
        dataset = Dataset()
        
        # Create observation with elemental composition that doesn't sum to 1.0
        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"
        
        # Elemental composition with fractions that don't sum to 1.0
        elem_comp1 = Dataset()
        elem_comp1.ROIElementalCompositionAtomicNumber = 6  # Carbon
        elem_comp1.ROIElementalCompositionAtomicMassFraction = 0.3
        
        elem_comp2 = Dataset()
        elem_comp2.ROIElementalCompositionAtomicNumber = 8  # Oxygen
        elem_comp2.ROIElementalCompositionAtomicMassFraction = 0.5  # Total = 0.8, not 1.0
        
        phys_props = Dataset()
        phys_props.ROIPhysicalProperty = "ELEM_FRACTION"
        phys_props.ROIPhysicalPropertyValue = 1.0
        phys_props.ROIElementalCompositionSequence = [elem_comp1, elem_comp2]
        
        obs_item.ROIPhysicalPropertiesSequence = [phys_props]
        dataset.RTROIObservationsSequence = [obs_item]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.warnings) >= 1
        assert any("Sum of ROI Elemental Composition Atomic Mass Fractions" in warning for warning in result.warnings)
    
    def test_validation_config_parameters_respected(self):
        """Test that ValidationConfig parameters control validation behavior."""
        dataset = Dataset()
        
        # Create dataset with invalid enumerated value
        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "INVALID_TYPE"
        obs_item.ROIInterpreter = "Dr. Test"
        
        dataset.RTROIObservationsSequence = [obs_item]
        
        # Test with enumerated values validation disabled
        config_no_enums = ValidationConfig(check_enumerated_values=False)
        result = RTROIObservationsValidator.validate(dataset, config_no_enums)
        
        # Should not have enumerated value errors
        enum_errors = [error for error in result.errors if "is not valid" in error]
        assert len(enum_errors) == 0
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        
        # Create incomplete observation
        incomplete_obs = Dataset()
        # Missing required elements
        
        dataset.RTROIObservationsSequence = [incomplete_obs]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) > 0
        # Check that error messages include DICOM tag references
        assert any("(3006,0082)" in error for error in result.errors)  # Observation Number
        assert any("(3006,0084)" in error for error in result.errors)  # Referenced ROI Number
        assert any("(3006,00A4)" in error for error in result.errors)  # RT ROI Interpreted Type
        assert any("(3006,00A6)" in error for error in result.errors)  # ROI Interpreter
    
    def test_error_messages_provide_guidance(self):
        """Test that error messages provide actionable guidance."""
        dataset = Dataset()
        
        incomplete_obs = Dataset()
        dataset.RTROIObservationsSequence = [incomplete_obs]
        
        result = RTROIObservationsValidator.validate(dataset)
        
        assert len(result.errors) > 0
        # Check that error messages provide helpful guidance
        assert any("uniquely identifies" in error.lower() for error in result.errors)
        assert any("must reference" in error.lower() for error in result.errors)
        assert any("describes the class" in error.lower() for error in result.errors)


class TestRTROIObservationsValidatorSequences:
    """Test validation of nested sequences within RT ROI Observations."""

    def test_validate_rt_related_roi_sequence(self):
        """Test validation of RT Related ROI Sequence."""
        dataset = Dataset()

        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"

        # Add related ROI with missing required element
        related_roi = Dataset()
        # Missing ReferencedROINumber (Type 1)
        related_roi.RTROIRelationship = "ENCLOSED"

        obs_item.RTRelatedROISequence = [related_roi]
        dataset.RTROIObservationsSequence = [obs_item]

        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.errors) >= 1
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)

    def test_validate_roi_physical_properties_sequence(self):
        """Test validation of ROI Physical Properties Sequence."""
        dataset = Dataset()

        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"

        # Add physical properties with missing required elements
        phys_props = Dataset()
        # Missing ROIPhysicalProperty (Type 1)
        # Missing ROIPhysicalPropertyValue (Type 1)

        obs_item.ROIPhysicalPropertiesSequence = [phys_props]
        dataset.RTROIObservationsSequence = [obs_item]

        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.errors) >= 2
        assert any("ROI Physical Property (3006,00B2) is required" in error for error in result.errors)
        assert any("ROI Physical Property Value (3006,00B4) is required" in error for error in result.errors)

    def test_validate_roi_elemental_composition_sequence(self):
        """Test validation of ROI Elemental Composition Sequence."""
        dataset = Dataset()

        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"

        # Add elemental composition with missing required elements
        elem_comp = Dataset()
        # Missing ROIElementalCompositionAtomicNumber (Type 1)
        # Missing ROIElementalCompositionAtomicMassFraction (Type 1)

        phys_props = Dataset()
        phys_props.ROIPhysicalProperty = "ELEM_FRACTION"
        phys_props.ROIPhysicalPropertyValue = 1.0
        phys_props.ROIElementalCompositionSequence = [elem_comp]

        obs_item.ROIPhysicalPropertiesSequence = [phys_props]
        dataset.RTROIObservationsSequence = [obs_item]

        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.errors) >= 2
        assert any("ROI Elemental Composition Atomic Number (3006,00B7) is required" in error for error in result.errors)
        assert any("ROI Elemental Composition Atomic Mass Fraction (3006,00B8) is required" in error for error in result.errors)


class TestRTROIObservationsValidatorSemanticConstraints:
    """Test semantic validation and consistency checks."""

    def test_validate_datetime_format(self):
        """Test validation of ROI Observation DateTime format."""
        dataset = Dataset()

        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"
        obs_item.ROIObservationDateTime = "INVALID_DATE"  # Invalid format

        dataset.RTROIObservationsSequence = [obs_item]

        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.warnings) >= 1
        assert any("ROI Observation DateTime" in warning and "should follow DICOM DateTime format" in warning for warning in result.warnings)

    def test_validate_atomic_number_range(self):
        """Test validation of atomic number ranges."""
        dataset = Dataset()

        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"
        obs_item.ROIInterpreter = "Dr. Test"

        # Invalid atomic number (outside 1-118 range)
        elem_comp = Dataset()
        elem_comp.ROIElementalCompositionAtomicNumber = 150  # Invalid
        elem_comp.ROIElementalCompositionAtomicMassFraction = 1.0

        phys_props = Dataset()
        phys_props.ROIPhysicalProperty = "ELEM_FRACTION"
        phys_props.ROIPhysicalPropertyValue = 1.0
        phys_props.ROIElementalCompositionSequence = [elem_comp]

        obs_item.ROIPhysicalPropertiesSequence = [phys_props]
        dataset.RTROIObservationsSequence = [obs_item]

        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.warnings) >= 1
        assert any("Atomic Number (150) should be between 1 and 118" in warning for warning in result.warnings)

    def test_validate_therapeutic_role_consistency(self):
        """Test validation of therapeutic role consistency with interpreted type."""
        dataset = Dataset()

        obs_item = Dataset()
        obs_item.ObservationNumber = 1
        obs_item.ReferencedROINumber = 1
        obs_item.RTROIInterpretedType = "PTV"  # Should have therapeutic role
        obs_item.ROIInterpreter = "Dr. Test"
        # Missing TherapeuticRoleCategoryCodeSequence for PTV

        dataset.RTROIObservationsSequence = [obs_item]

        result = RTROIObservationsValidator.validate(dataset)

        assert len(result.warnings) >= 1
        assert any("typically requires Therapeutic Role Category Code Sequence" in warning for warning in result.warnings)
