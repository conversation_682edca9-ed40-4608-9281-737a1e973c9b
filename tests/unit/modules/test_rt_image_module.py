"""
Test RTImageModule functionality.

RTImageModule implements DICOM PS3.3 C.8.8.2 RT Image Module.
Describes RT-specific characteristics of a projection image.
"""

from pyrt_dicom.modules import RTImageModule
from pyrt_dicom.enums.rt_enums import (
    RTImagePlane, PrimaryDosimeterUnit, PixelIntensityRelationshipSign,
    RTImageTypeValue3, RTBeamLimitingDeviceType, BlockType, BlockDivergence,
    BlockMountingPosition, FluenceDataSource, EnhancedRTBeamLimitingDeviceDefinitionFlag,
    ReportedValuesOrigin
)
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, PixelRepresentation
from pyrt_dicom.enums.series_enums import PatientPosition
from pyrt_dicom.validators import ValidationResult, ValidationError


class TestRTImageModule:
    """Test RTImageModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image 1",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        # Access attributes through to_dataset() method
        dataset = rt_image.to_dataset()
        assert dataset.SamplesPerPixel == 1
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.BitsAllocated == 16
        assert dataset.BitsStored == 16
        assert dataset.HighBit == 15
        assert dataset.PixelRepresentation == 0  # UNSIGNED
        assert dataset.RTImageLabel == "Portal Image 1"
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "PORTAL"]
        assert dataset.RTImagePlane == "NORMAL"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default empty values for Type 2 elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        
        # Type 2 element should be empty string
        dataset = rt_image.to_dataset()
        assert dataset.ConversionType == ""
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            rt_image_name="Portal verification image",
            rt_image_description="Verification image for beam 1",
            radiation_machine_name="Varian TrueBeam",
            primary_dosimeter_unit=PrimaryDosimeterUnit.MU,
            radiation_machine_sad=1000.0,
            rt_image_sid=1500.0
        )
        
        dataset = rt_image.to_dataset()
        assert dataset.RTImageName == "Portal verification image"
        assert dataset.RTImageDescription == "Verification image for beam 1"
        assert dataset.RadiationMachineName == "Varian TrueBeam"
        assert dataset.PrimaryDosimeterUnit == "MU"
        assert dataset.RadiationMachineSAD == 1000.0
        assert dataset.RTImageSID == 1500.0
    
    def test_with_pixel_intensity_relationship_sign(self):
        """Test adding pixel intensity relationship sign."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            pixel_intensity_relationship="LIN"
        ).with_pixel_intensity_relationship_sign(
            pixel_intensity_relationship_sign=PixelIntensityRelationshipSign.POSITIVE
        )
        
        dataset = rt_image.to_dataset()
        assert dataset.PixelIntensityRelationship == "LIN"
        assert dataset.PixelIntensityRelationshipSign == 1  # POSITIVE
    
    def test_with_exposure_sequence(self):
        """Test adding exposure sequence."""
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0,
            exposure_time=100.0,
            meterset_exposure=50.0
        )
        
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_exposure_sequence([exposure_item])
        
        dataset = rt_image.to_dataset()
        assert hasattr(dataset, 'ExposureSequence')
        assert len(dataset.ExposureSequence) == 1
        assert dataset.ExposureSequence[0].KVP == 120.0
        assert dataset.ExposureSequence[0].XRayTubeCurrent == 200.0
        assert dataset.ExposureSequence[0].ExposureTime == 100.0
        assert dataset.ExposureSequence[0].MetersetExposure == 50.0
    
    def test_create_beam_limiting_device_item(self):
        """Test creating beam limiting device item."""
        device_item = RTImageModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.ASYMX,
            number_of_leaf_jaw_pairs=1,
            source_to_beam_limiting_device_distance=400.0,
            leaf_jaw_positions=[-50.0, 50.0]
        )
        
        assert device_item.RTBeamLimitingDeviceType == "ASYMX"
        assert device_item.NumberOfLeafJawPairs == 1
        assert device_item.SourceToBeamLimitingDeviceDistance == 400.0
        assert device_item.LeafJawPositions == [-50.0, 50.0]
    
    def test_create_block_item(self):
        """Test creating block item."""
        block_item = RTImageModule.create_block_item(
            block_number=1,
            block_type=BlockType.SHIELDING,
            block_divergence=BlockDivergence.PRESENT,
            source_to_block_tray_distance=500.0,
            material_id="LEAD",
            block_number_of_points=4,
            block_data=[-50.0, -50.0, 50.0, -50.0, 50.0, 50.0, -50.0, 50.0],
            block_name="Custom Block",
            block_thickness=70.0
        )
        
        assert block_item.BlockNumber == 1
        assert block_item.BlockType == "SHIELDING"
        assert block_item.BlockDivergence == "PRESENT"
        assert block_item.SourceToBlockTrayDistance == 500.0
        assert block_item.MaterialID == "LEAD"
        assert block_item.BlockNumberOfPoints == 4
        assert len(block_item.BlockData) == 8  # 4 points (x,y)
        assert block_item.BlockName == "Custom Block"
        assert block_item.BlockThickness == 70.0
    
    def test_create_block_item_with_mounting_position(self):
        """Test creating block item with block mounting position."""
        block_item = RTImageModule.create_block_item(
            block_number=2,
            block_type=BlockType.APERTURE,
            block_divergence=BlockDivergence.ABSENT,
            source_to_block_tray_distance=450.0,
            material_id="TUNGSTEN",
            block_number_of_points=6,
            block_data=[-30.0, -30.0, 30.0, -30.0, 30.0, 30.0, -30.0, 30.0, 0.0, 40.0, 0.0, -40.0],
            block_mounting_position=BlockMountingPosition.PATIENT_SIDE,
            block_name="Aperture Block",
            block_thickness=50.0
        )
        
        assert block_item.BlockNumber == 2
        assert block_item.BlockType == "APERTURE"
        assert block_item.BlockDivergence == "ABSENT"
        assert block_item.BlockMountingPosition == "PATIENT_SIDE"
        assert block_item.BlockName == "Aperture Block"
        assert block_item.MaterialID == "TUNGSTEN"
    
    def test_property_methods(self):
        """Test property methods."""
        # Test portal image
        portal_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        assert portal_image.is_portal_image is True
        assert portal_image.is_simulator_image is False
        assert portal_image.is_fluence_map is False
        
        # Test fluence map
        fluence_map = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Fluence Map",
            image_type=["ORIGINAL", "PRIMARY", "FLUENCE"]
        )
        assert fluence_map.is_fluence_map is True
        
        # Test exposure data
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0
        )
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_exposure_sequence([exposure_item])
        
        assert rt_image.has_exposure_data is True
    
    def test_with_fluence_map_sequence(self):
        """Test adding fluence map sequence with fluence data source."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Fluence Map",
            image_type=["ORIGINAL", "PRIMARY", "FLUENCE"]
        ).with_fluence_map_sequence(
            fluence_data_source=FluenceDataSource.CALCULATED,
            fluence_data_scale=1.0
        )
        
        dataset = rt_image.to_dataset()
        assert hasattr(dataset, 'FluenceMapSequence')
        assert len(dataset.FluenceMapSequence) == 1
        assert dataset.FluenceMapSequence[0].FluenceDataSource == "CALCULATED"
        assert dataset.FluenceMapSequence[0].FluenceDataScale == 1.0
        assert rt_image.is_fluence_map is True
    
    def test_with_enhanced_beam_limiting_device_flag(self):
        """Test adding enhanced RT beam limiting device definition flag."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Enhanced Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            enhanced_rt_beam_limiting_device_definition_flag=EnhancedRTBeamLimitingDeviceDefinitionFlag.YES
        )
        
        dataset = rt_image.to_dataset()
        assert dataset.EnhancedRTBeamLimitingDeviceDefinitionFlag == "YES"
    
    def test_with_patient_position(self):
        """Test adding patient position when isocenter position is present."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Position Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            isocenter_position=[0.0, 0.0, 0.0]
        ).with_patient_position(
            patient_position=PatientPosition.HFS
        )
        
        dataset = rt_image.to_dataset()
        assert dataset.IsocenterPosition == [0.0, 0.0, 0.0]
        assert dataset.PatientPosition == "HFS"
    
    def test_different_image_types_with_rt_image_type_value3(self):
        """Test different RT image types using RTImageTypeValue3 enum values."""
        # Test DRR image
        drr_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="DRR Image",
            image_type=["ORIGINAL", "PRIMARY", RTImageTypeValue3.DRR.value]
        )
        
        dataset = drr_image.to_dataset()
        assert dataset.ImageType[2] == "DRR"
        assert not drr_image.is_portal_image
        assert not drr_image.is_fluence_map
        
        # Test Simulator image
        simulator_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Simulator Image",
            image_type=["ORIGINAL", "PRIMARY", RTImageTypeValue3.SIMULATOR.value]
        )
        
        dataset2 = simulator_image.to_dataset()
        assert dataset2.ImageType[2] == "SIMULATOR"
        assert simulator_image.is_simulator_image
        
        # Test Radiograph image
        radiograph_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Radiograph Image",
            image_type=["ORIGINAL", "PRIMARY", RTImageTypeValue3.RADIOGRAPH.value]
        )
        
        dataset3 = radiograph_image.to_dataset()
        assert dataset3.ImageType[2] == "RADIOGRAPH"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )

        assert hasattr(rt_image, 'validate')
        assert callable(rt_image.validate)

        # Test validation result structure
        validation_result = rt_image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_public_validation_convenience_methods(self):
        """Test public validation convenience methods with zero-copy optimization."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )

        # Test check_required_elements
        assert hasattr(rt_image, 'check_required_elements')
        assert callable(rt_image.check_required_elements)
        result = rt_image.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass with all required elements

        # Test check_conditional_requirements
        assert hasattr(rt_image, 'check_conditional_requirements')
        assert callable(rt_image.check_conditional_requirements)
        result = rt_image.check_conditional_requirements()
        assert isinstance(result, ValidationResult)

        # Test check_enum_constraints
        assert hasattr(rt_image, 'check_enum_constraints')
        assert callable(rt_image.check_enum_constraints)
        result = rt_image.check_enum_constraints()
        assert isinstance(result, ValidationResult)

        # Test check_sequence_requirements
        assert hasattr(rt_image, 'check_sequence_requirements')
        assert callable(rt_image.check_sequence_requirements)
        result = rt_image.check_sequence_requirements()
        assert isinstance(result, ValidationResult)

    def test_private_validation_methods_exist(self):
        """Test that private validation methods exist and are callable."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        ).with_reported_values_origin(ReportedValuesOrigin.ACTUAL)  # Required for PORTAL images

        # Test private methods exist
        assert hasattr(rt_image, '_ensure_required_elements_valid')
        assert callable(rt_image._ensure_required_elements_valid)
        assert hasattr(rt_image, '_ensure_conditional_requirements_valid')
        assert callable(rt_image._ensure_conditional_requirements_valid)
        assert hasattr(rt_image, '_ensure_enum_constraints_valid')
        assert callable(rt_image._ensure_enum_constraints_valid)
        assert hasattr(rt_image, '_ensure_sequence_requirements_valid')
        assert callable(rt_image._ensure_sequence_requirements_valid)

        # Test that private methods don't raise exceptions for valid data
        try:
            rt_image._ensure_required_elements_valid()
            rt_image._ensure_conditional_requirements_valid()
            rt_image._ensure_enum_constraints_valid()
            rt_image._ensure_sequence_requirements_valid()
        except Exception as e:
            assert False, f"Private validation methods should not raise exceptions for valid data: {e}"

    def test_zero_copy_validation_optimization(self):
        """Test that validation uses zero-copy optimization (passes self instead of to_dataset())."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )

        # Test that validation methods work with BaseModule instance directly
        # This implicitly tests zero-copy since the validator methods accept Dataset | BaseModule
        result = rt_image.validate()
        assert isinstance(result, ValidationResult)

        result = rt_image.check_required_elements()
        assert isinstance(result, ValidationResult)

        result = rt_image.check_conditional_requirements()
        assert isinstance(result, ValidationResult)

        result = rt_image.check_enum_constraints()
        assert isinstance(result, ValidationResult)

        result = rt_image.check_sequence_requirements()
        assert isinstance(result, ValidationResult)

    def test_type_1c_conditional_validation_scenarios(self):
        """Test Type 1C conditional validation scenarios as defined in DICOM standard."""
        # Test Pixel Intensity Relationship Sign required when Pixel Intensity Relationship is present
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        ).with_optional_elements(
            pixel_intensity_relationship="LIN"
        )

        # Should fail conditional requirements validation
        result = rt_image.check_conditional_requirements()
        assert len(result.errors) > 0
        assert any("Pixel Intensity Relationship Sign" in error for error in result.errors)

        # Add the required sign - should pass
        rt_image.with_pixel_intensity_relationship_sign(PixelIntensityRelationshipSign.POSITIVE)
        result = rt_image.check_conditional_requirements()
        pixel_intensity_errors = [error for error in result.errors if "Pixel Intensity Relationship Sign" in error]
        assert len(pixel_intensity_errors) == 0

        # Test Patient Position required when Isocenter Position is present
        rt_image_with_isocenter = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        ).with_optional_elements(
            isocenter_position=[0.0, 0.0, 0.0]
        )

        # Should fail conditional requirements validation
        result = rt_image_with_isocenter.check_conditional_requirements()
        assert len(result.errors) > 0
        assert any("Patient Position" in error for error in result.errors)

        # Add Patient Position - should pass
        rt_image_with_isocenter.with_patient_position(PatientPosition.HFS)
        result = rt_image_with_isocenter.check_conditional_requirements()
        patient_position_errors = [error for error in result.errors if "Patient Position" in error]
        assert len(patient_position_errors) == 0

    def test_type_2c_conditional_validation_scenarios(self):
        """Test Type 2C conditional validation scenarios as defined in DICOM standard."""
        # Test RT Image Orientation required when RT Image Plane is NON_NORMAL
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NON_NORMAL
        )

        # Should fail conditional requirements validation
        result = rt_image.check_conditional_requirements()
        assert len(result.errors) > 0
        assert any("RT Image Orientation" in error for error in result.errors)

        # Add RT Image Orientation - should pass
        rt_image.with_rt_image_orientation([1.0, 0.0, 0.0, 0.0, 1.0, 0.0])
        result = rt_image.check_conditional_requirements()
        orientation_errors = [error for error in result.errors if "RT Image Orientation" in error]
        assert len(orientation_errors) == 0

    def test_validation_error_exceptions(self):
        """Test that private validation methods raise ValidationError exceptions appropriately."""
        # Create module with missing required elements
        rt_image = RTImageModule()

        # Should raise ValidationError for missing required elements
        try:
            rt_image._ensure_required_elements_valid()
            assert False, "Should have raised ValidationError for missing required elements"
        except ValidationError as e:
            assert "Required elements validation failed" in str(e)

        # Create module with conditional requirement violation
        rt_image_conditional = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NON_NORMAL  # Requires RT Image Orientation
        )

        # Should raise ValidationError for conditional requirement violation
        try:
            rt_image_conditional._ensure_conditional_requirements_valid()
            assert False, "Should have raised ValidationError for conditional requirement violation"
        except ValidationError as e:
            assert "Conditional requirements validation failed" in str(e)
    
    
    def test_method_chaining(self):
        """Test that methods support chaining."""
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0
        )
        
        rt_image = (RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Chained Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        .with_optional_elements(
            rt_image_name="Chained Name",
            radiation_machine_name="Varian TrueBeam"
        )
        .with_exposure_sequence([exposure_item]))
        
        dataset = rt_image.to_dataset()
        assert dataset.RTImageLabel == "Chained Image"
        assert dataset.RTImageName == "Chained Name"
        assert dataset.RadiationMachineName == "Varian TrueBeam"
        assert rt_image.has_exposure_data is True
    
    def test_to_dataset_method(self):
        """Test that to_dataset() generates valid pydicom Dataset."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Dataset Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        ).with_optional_elements(
            rt_image_name="Test Name",
            radiation_machine_name="Test Machine"
        )
        
        # Test dataset generation
        dataset = rt_image.to_dataset()
        
        # Verify it's a proper pydicom Dataset
        from pydicom import Dataset
        assert isinstance(dataset, Dataset)
        
        # Verify all attributes are present
        assert dataset.SamplesPerPixel == 1
        assert dataset.RTImageLabel == "Dataset Test Image"
        assert dataset.RTImageName == "Test Name"
        assert dataset.RadiationMachineName == "Test Machine"
        
        # Verify dataset has expected length
        assert len(dataset) > 0
        
        # Test that multiple calls return independent copies
        dataset2 = rt_image.to_dataset()
        assert dataset is not dataset2  # Different objects
        assert dataset.RTImageLabel == dataset2.RTImageLabel  # Same data
    
    def test_module_base_properties(self):
        """Test inherited BaseModule properties work correctly."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Properties Test",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        # Test BaseModule properties
        assert rt_image.module_name == "RTImageModule"
        assert rt_image.has_data is True
        assert rt_image.get_element_count() > 0
        
        # Test string representation
        repr_str = repr(rt_image)
        assert "RTImageModule" in repr_str
        assert "10 attributes" in repr_str
