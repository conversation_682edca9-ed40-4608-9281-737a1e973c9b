"""
Test RTPatientSetupModule functionality.

RTPatientSetupModule implements DICOM PS3.3 C.8.8.12 RT Patient Setup Module
using composition-based architecture with internal dataset management.
Contains information describing patient positioning and fixation devices.
"""

import pytest
import pydicom
from pyrt_dicom.modules import RTPatientSetupModule
from pyrt_dicom.enums.rt_enums import (
    FixationDeviceType, ShieldingDeviceType, SetupTechnique, SetupDeviceType,
    RespiratoryMotionCompensationTechnique, RespiratorySignalSource
)
from pyrt_dicom.enums.series_enums import PatientPosition
from pyrt_dicom.validators import ValidationResult


class TestRTPatientSetupModule:
    """Test RTPatientSetupModule functionality with composition-based architecture."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        patient_setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[patient_setup_item]
        )

        # Test internal dataset management
        assert hasattr(module._dataset, 'PatientSetupSequence')
        assert len(module.PatientSetupSequence) == 1
        assert module.PatientSetupSequence[0].PatientSetupNumber == 1
        assert module.PatientSetupSequence[0].PatientPosition == "HFS"

        # Test dataset generation
        dataset = module.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert hasattr(dataset, 'PatientSetupSequence')
        assert len(dataset.PatientSetupSequence) == 1
        assert dataset.PatientSetupSequence[0].PatientSetupNumber == 1
        assert dataset.PatientSetupSequence[0].PatientPosition == "HFS"
    
    def test_patient_setup_item_creation(self):
        """Test creation of patient setup sequence items."""
        # Test with patient position
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            patient_setup_label="Standard Setup",
            setup_technique=SetupTechnique.ISOCENTRIC
        )

        assert isinstance(setup_item, pydicom.Dataset)
        assert setup_item.PatientSetupNumber == 1
        assert setup_item.PatientPosition == "HFS"
        assert setup_item.PatientSetupLabel == "Standard Setup"

        # Test with patient additional position
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=2,
            patient_additional_position="Custom Position"
        )

        assert isinstance(setup_item, pydicom.Dataset)
        assert setup_item.PatientSetupNumber == 2
        assert setup_item.PatientAdditionalPosition == "Custom Position"
        assert not hasattr(setup_item, 'PatientPosition')
    
    def test_patient_setup_conditional_requirement(self):
        """Test conditional requirement for patient position or additional position."""
        # Should raise error when neither position nor additional position provided
        with pytest.raises(ValueError, match="Either Patient Position or Patient Additional Position must be present"):
            RTPatientSetupModule.create_patient_setup_item(
                patient_setup_number=1
            )
    
    def test_fixation_device_item_creation(self):
        """Test creation of fixation device sequence items."""
        fixation_device = RTPatientSetupModule.create_fixation_device_item(
            fixation_device_type=FixationDeviceType.MASK,
            fixation_device_label="Head Mask",
            fixation_device_description="Custom thermoplastic mask"
        )

        assert isinstance(fixation_device, pydicom.Dataset)
        assert fixation_device.FixationDeviceType == "MASK"
        assert fixation_device.FixationDeviceLabel == "Head Mask"
        assert fixation_device.FixationDeviceDescription == "Custom thermoplastic mask"
    
    def test_shielding_device_item_creation(self):
        """Test creation of shielding device sequence items."""
        shielding_device = RTPatientSetupModule.create_shielding_device_item(
            shielding_device_type=ShieldingDeviceType.EYE,
            shielding_device_label="Eye Shield",
            shielding_device_description="Custom eye shield"
        )

        assert isinstance(shielding_device, pydicom.Dataset)
        assert shielding_device.ShieldingDeviceType == "EYE"
        assert shielding_device.ShieldingDeviceLabel == "Eye Shield"
        assert shielding_device.ShieldingDeviceDescription == "Custom eye shield"
    
    def test_setup_device_item_creation(self):
        """Test creation of setup device sequence items."""
        setup_device = RTPatientSetupModule.create_setup_device_item(
            setup_device_type=SetupDeviceType.LASER_POINTER,
            setup_device_label="Positioning Laser",
            setup_device_parameter=90.0,
            setup_device_description="Room laser system"
        )

        assert isinstance(setup_device, pydicom.Dataset)
        assert setup_device.SetupDeviceType == "LASER_POINTER"
        assert setup_device.SetupDeviceLabel == "Positioning Laser"
        assert setup_device.SetupDeviceParameter == 90.0
        assert setup_device.SetupDeviceDescription == "Room laser system"

    def test_motion_synchronization_item_creation(self):
        """Test creation of motion synchronization sequence items."""
        motion_sync = RTPatientSetupModule.create_motion_synchronization_item(
            respiratory_motion_compensation_technique=RespiratoryMotionCompensationTechnique.BREATH_HOLD,
            respiratory_signal_source=RespiratorySignalSource.BELT,
            respiratory_motion_compensation_technique_description="Deep inspiration breath hold"
        )

        assert isinstance(motion_sync, pydicom.Dataset)
        assert motion_sync.RespiratoryMotionCompensationTechnique == "BREATH_HOLD"
        assert motion_sync.RespiratorySignalSource == "BELT"
        assert motion_sync.RespiratoryMotionCompensationTechniqueDescription == "Deep inspiration breath hold"

    def test_referenced_setup_image_item_creation(self):
        """Test creation of referenced setup image sequence items."""
        setup_image = RTPatientSetupModule.create_referenced_setup_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.1",
            referenced_sop_instance_uid="*******.*******.9",
            setup_image_comment="Setup verification image"
        )

        assert isinstance(setup_image, pydicom.Dataset)
        assert setup_image.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.1"
        assert setup_image.ReferencedSOPInstanceUID == "*******.*******.9"
        assert setup_image.SetupImageComment == "Setup verification image"

    def test_code_sequence_item_creation(self):
        """Test creation of code sequence items."""
        code_item = RTPatientSetupModule.create_code_sequence_item(
            code_value="123456",
            coding_scheme_designator="DCM",
            code_meaning="Example Code"
        )

        assert isinstance(code_item, pydicom.Dataset)
        assert code_item.CodeValue == "123456"
        assert code_item.CodingSchemeDesignator == "DCM"
        assert code_item.CodeMeaning == "Example Code"
    
    def test_module_properties(self):
        """Test module convenience properties."""
        # Test empty module
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[]
        )

        # Even empty sequence creates the attribute, so has_patient_setups is True
        assert module.has_patient_setups
        assert module.patient_setup_count == 0
        assert module.get_patient_setup_numbers() == []

        # Test module with setups
        setup_item1 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        setup_item2 = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=2,
            patient_position=PatientPosition.HFP
        )

        # Static methods now return proper Dataset objects
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item1, setup_item2]
        )

        assert module.has_patient_setups
        assert module.patient_setup_count == 2
        assert module.get_patient_setup_numbers() == [1, 2]

        # Test getting setup by number
        found_setup = module.get_patient_setup_by_number(1)
        assert found_setup is not None
        assert found_setup.PatientSetupNumber == 1
        
        not_found_setup = module.get_patient_setup_by_number(99)
        assert not_found_setup is None
    
    def test_with_optional_elements(self):
        """Test with_optional_elements method."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        ).with_optional_elements()

        # Should return self for method chaining
        assert isinstance(module, RTPatientSetupModule)
        assert module.has_patient_setups

    def test_has_fixation_devices(self):
        """Test has_fixation_devices property."""
        # Test without fixation devices
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        assert not module.has_fixation_devices()
        
        # Test with fixation devices
        fixation_device = RTPatientSetupModule.create_fixation_device_item(
            fixation_device_type=FixationDeviceType.MASK,
            fixation_device_label="Head Mask"
        )

        setup_item_with_fixation = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            fixation_device_sequence=[fixation_device]
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item_with_fixation]
        )

        assert module.has_fixation_devices()

    def test_has_motion_synchronization(self):
        """Test has_motion_synchronization property."""
        # Test without motion synchronization
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )

        assert not module.has_motion_synchronization()
        
        # Test with motion synchronization
        motion_sync = RTPatientSetupModule.create_motion_synchronization_item(
            respiratory_motion_compensation_technique=RespiratoryMotionCompensationTechnique.BREATH_HOLD,
            respiratory_signal_source=RespiratorySignalSource.BELT
        )

        setup_item_with_motion = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            motion_synchronization_sequence=[motion_sync]
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item_with_motion]
        )

        assert module.has_motion_synchronization()

    def test_validation(self):
        """Test module validation."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )

        assert hasattr(module, 'validate')
        assert callable(module.validate)

        # Test validation result structure
        validation_result = module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_dataset_generation(self):
        """Test dataset generation functionality."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS,
            patient_setup_label="Test Setup"
        )

        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )

        # Test to_dataset method
        dataset = module.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert hasattr(dataset, 'PatientSetupSequence')
        assert len(dataset.PatientSetupSequence) == 1
        assert dataset.PatientSetupSequence[0].PatientSetupNumber == 1
        assert dataset.PatientSetupSequence[0].PatientPosition == "HFS"
        assert dataset.PatientSetupSequence[0].PatientSetupLabel == "Test Setup"

        # Test that dataset is a copy (not the same object)
        assert dataset is not module._dataset

        # Test module properties work correctly
        assert module.has_patient_setups
        assert module.patient_setup_count == 1
        assert module.get_patient_setup_numbers() == [1]

    def test_validation_methods_exist(self):
        """Test that all validation methods are present and callable."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # Test public validation convenience methods exist
        assert hasattr(module, 'check_required_elements')
        assert callable(module.check_required_elements)
        
        assert hasattr(module, 'check_conditional_requirements')
        assert callable(module.check_conditional_requirements)
        
        assert hasattr(module, 'check_enumerated_values')
        assert callable(module.check_enumerated_values)
        
        assert hasattr(module, 'check_sequence_structures')
        assert callable(module.check_sequence_structures)
        
        # Test private validation methods exist
        assert hasattr(module, '_ensure_required_elements_valid')
        assert callable(module._ensure_required_elements_valid)
        
        assert hasattr(module, '_ensure_conditional_requirements_valid')
        assert callable(module._ensure_conditional_requirements_valid)
        
        assert hasattr(module, '_ensure_enumerated_values_valid')
        assert callable(module._ensure_enumerated_values_valid)
        
        assert hasattr(module, '_ensure_sequence_structures_valid')
        assert callable(module._ensure_sequence_structures_valid)

    def test_zero_copy_validation(self):
        """Test that validation works with zero-copy (self passed directly)."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # Test main validate method uses zero-copy
        result = module.validate()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        
        # Test granular validation methods use zero-copy
        result = module.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        
        result = module.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_granular_validation_required_elements(self):
        """Test granular validation of required elements."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # Valid module should pass
        result = module.check_required_elements()
        assert len(result.errors) == 0
        
        # Test with empty sequence (should fail)
        empty_module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[]
        )
        result = empty_module.check_required_elements()
        assert len(result.errors) > 0
        
        # Should contain error about empty sequence
        error_messages = ' '.join(result.errors)
        assert "cannot be empty" in error_messages.lower() or "empty" in error_messages.lower()

    def test_granular_validation_conditional_requirements(self):
        """Test granular validation of conditional requirements."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # Valid module should pass
        result = module.check_conditional_requirements()
        assert len(result.errors) == 0

    def test_granular_validation_enumerated_values(self):
        """Test granular validation of enumerated values."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # Valid module should pass
        result = module.check_enumerated_values()
        assert len(result.errors) == 0 or len(result.warnings) >= 0  # May have warnings

    def test_granular_validation_sequence_structures(self):
        """Test granular validation of sequence structures."""
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # Valid module should pass
        result = module.check_sequence_structures()
        assert len(result.errors) == 0

    def test_private_validation_methods_raise_exceptions(self):
        """Test that private validation methods raise ValidationError on failure."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        # Test with valid module (should not raise)
        setup_item = RTPatientSetupModule.create_patient_setup_item(
            patient_setup_number=1,
            patient_position=PatientPosition.HFS
        )
        
        module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[setup_item]
        )
        
        # These should not raise for valid module
        try:
            module._ensure_required_elements_valid()
            module._ensure_conditional_requirements_valid()
            module._ensure_enumerated_values_valid()
            module._ensure_sequence_structures_valid()
        except ValidationError:
            pytest.fail("Private validation methods should not raise for valid module")
        
        # Test with invalid module (empty sequence)
        empty_module = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[]
        )
        
        # This should raise ValidationError
        with pytest.raises(ValidationError):
            empty_module._ensure_required_elements_valid()

    def test_validation_error_import(self):
        """Test ValidationError can be imported and used."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        # Test it's a proper exception class
        assert issubclass(ValidationError, Exception)
        
        # Test it can be instantiated
        error = ValidationError("Test message")
        assert str(error) == "Test message"
    