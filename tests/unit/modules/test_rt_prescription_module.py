"""
Test RTPrescriptionModule (Type 3) functionality.

RTPrescriptionModule implements DICOM PS3.3 C.8.8.10 RT Prescription Module.
Contains prescription information for radiotherapy treatment.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTPrescriptionModule
from pyrt_dicom.enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation
from pyrt_dicom.validators import ValidationResult


class TestRTPrescriptionModule:
    """Test RTPrescriptionModule (Type 3) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Should create an empty module since all elements are Type 3 (optional)
        assert prescription is not None
        assert isinstance(prescription, RTPrescriptionModule)
        
        dataset = prescription.to_dataset()
        assert not hasattr(dataset, 'PrescriptionDescription')
        assert not hasattr(dataset, 'DoseReferenceSequence')
    
    def test_with_optional_elements_prescription_description(self):
        """Test adding prescription description optional element."""
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            prescription_description="Primary treatment prescription"
        )
        
        dataset = prescription.to_dataset()
        assert hasattr(dataset, 'PrescriptionDescription')
        assert dataset.PrescriptionDescription == "Primary treatment prescription"
    
    def test_with_optional_elements_dose_reference_sequence(self):
        """Test adding dose reference sequence optional element."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1,
            dose_value_purpose=DoseValuePurpose.TRACKING,
            dose_value_interpretation=DoseValueInterpretation.ACTUAL
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        dataset = prescription.to_dataset()
        assert hasattr(dataset, 'DoseReferenceSequence')
        assert len(dataset.DoseReferenceSequence) == 1
        assert dataset.DoseReferenceSequence[0].DoseReferenceNumber == 1
    
    def test_create_dose_reference_item_point_type(self):
        """Test creating dose reference item with POINT structure type."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1,
            target_prescription_dose=200.0
        )
        
        assert dose_ref_item.DoseReferenceNumber == 1
        assert dose_ref_item.DoseReferenceStructureType == "POINT"
        assert dose_ref_item.DoseReferenceType == "TARGET"
        assert dose_ref_item.ReferencedROINumber == 1
        assert dose_ref_item.TargetPrescriptionDose == 200.0
    
    def test_create_dose_reference_item_volume_type(self):
        """Test creating dose reference item with VOLUME structure type."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2,
            organ_at_risk_maximum_dose=1000.0
        )
        
        assert dose_ref_item.DoseReferenceNumber == 2
        assert dose_ref_item.DoseReferenceStructureType == "VOLUME"
        assert dose_ref_item.DoseReferenceType == "ORGAN_AT_RISK"
        assert dose_ref_item.ReferencedROINumber == 2
        assert dose_ref_item.OrganAtRiskMaximumDose == 1000.0
    
    def test_create_dose_reference_item_coordinates_type(self):
        """Test creating dose reference item with COORDINATES structure type."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=3,
            dose_reference_structure_type=DoseReferenceStructureType.COORDINATES,
            dose_reference_type=DoseReferenceType.TARGET,
            dose_reference_point_coordinates=[100.0, 200.0, 300.0]
        )
        
        assert dose_ref_item.DoseReferenceNumber == 3
        assert dose_ref_item.DoseReferenceStructureType == "COORDINATES"
        assert dose_ref_item.DoseReferenceType == "TARGET"
        assert dose_ref_item.DoseReferencePointCoordinates == [100.0, 200.0, 300.0]
    
    def test_create_dose_reference_item_conditional_validation(self):
        """Test conditional validation for dose reference items."""
        # Test error when POINT type without referenced_roi_number
        with pytest.raises(ValueError, match="Referenced ROI Number is required"):
            RTPrescriptionModule.create_dose_reference_item(
                dose_reference_number=1,
                dose_reference_structure_type=DoseReferenceStructureType.POINT,
                dose_reference_type=DoseReferenceType.TARGET
            )
        
        # Test error when COORDINATES type without coordinates
        with pytest.raises(ValueError, match="Dose Reference Point Coordinates is required"):
            RTPrescriptionModule.create_dose_reference_item(
                dose_reference_number=1,
                dose_reference_structure_type=DoseReferenceStructureType.COORDINATES,
                dose_reference_type=DoseReferenceType.TARGET
            )
    
    def test_has_prescription_property(self):
        """Test has_prescription property."""
        # Empty prescription
        prescription = RTPrescriptionModule.from_required_elements()
        assert not prescription.has_prescription
        
        # With prescription description
        prescription.with_optional_elements(prescription_description="Test prescription")
        assert prescription.has_prescription
        
        # With dose reference sequence
        empty_prescription = RTPrescriptionModule.from_required_elements()
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        empty_prescription.with_optional_elements(dose_reference_sequence=[dose_ref_item])
        assert empty_prescription.has_prescription
    
    def test_dose_reference_count_property(self):
        """Test dose_reference_count property."""
        prescription = RTPrescriptionModule.from_required_elements()
        assert prescription.dose_reference_count == 0
        
        dose_ref_item1 = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        dose_ref_item2 = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription.with_optional_elements(dose_reference_sequence=[dose_ref_item1, dose_ref_item2])
        assert prescription.dose_reference_count == 2
    
    def test_get_dose_reference_types(self):
        """Test get_dose_reference_types method."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Empty sequence
        assert prescription.get_dose_reference_types() == []
        
        # With mixed types
        target_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        oar_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription.with_optional_elements(dose_reference_sequence=[target_item, oar_item])
        reference_types = prescription.get_dose_reference_types()
        assert "TARGET" in reference_types
        assert "ORGAN_AT_RISK" in reference_types
        assert len(reference_types) == 2
    
    def test_get_target_dose_references(self):
        """Test get_target_dose_references method."""
        target_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        oar_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[target_item, oar_item]
        )
        
        target_refs = prescription.get_target_dose_references()
        assert len(target_refs) == 1
        assert target_refs[0].DoseReferenceType == "TARGET"
        assert target_refs[0].DoseReferenceNumber == 1
    
    def test_get_organ_at_risk_dose_references(self):
        """Test get_organ_at_risk_dose_references method."""
        target_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        oar_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[target_item, oar_item]
        )
        
        oar_refs = prescription.get_organ_at_risk_dose_references()
        assert len(oar_refs) == 1
        assert oar_refs[0].DoseReferenceType == "ORGAN_AT_RISK"
        assert oar_refs[0].DoseReferenceNumber == 2
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        assert hasattr(prescription, 'validate')
        assert callable(prescription.validate)
        
        # Test validation result structure
        validation_result = prescription.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_to_dataset_generation(self):
        """Test dataset generation from module."""
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            prescription_description="Test prescription"
        )
        
        dataset = prescription.to_dataset()
        assert isinstance(dataset, Dataset)
        assert hasattr(dataset, 'PrescriptionDescription')
        assert dataset.PrescriptionDescription == "Test prescription"
        
        # Test that modifications to returned dataset don't affect module
        dataset.PrescriptionDescription = "Modified prescription"
        new_dataset = prescription.to_dataset()
        assert new_dataset.PrescriptionDescription == "Test prescription"
    
    
    def test_string_enum_values(self):
        """Test using string values instead of enum objects."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type="POINT",  # String instead of enum
            dose_reference_type="TARGET",           # String instead of enum
            referenced_roi_number=1
        )
        
        assert dose_ref_item.DoseReferenceStructureType == "POINT"
        assert dose_ref_item.DoseReferenceType == "TARGET"
    
    def test_method_chaining(self):
        """Test method chaining functionality."""
        prescription = (RTPrescriptionModule.from_required_elements()
                       .with_optional_elements(prescription_description="Test prescription"))
        
        dataset = prescription.to_dataset()
        assert hasattr(dataset, 'PrescriptionDescription')
        assert dataset.PrescriptionDescription == "Test prescription"
    
    def test_check_required_elements_empty_module(self):
        """Test check_required_elements with empty module."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Should pass since there are no required elements at top level 
        result = prescription.check_required_elements()
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_required_elements_with_incomplete_dose_reference(self):
        """Test check_required_elements with incomplete dose reference items."""
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Create incomplete dose reference item (missing required fields)
        incomplete_item = Dataset()
        # Missing DoseReferenceNumber, DoseReferenceStructureType, DoseReferenceType
        
        prescription.with_optional_elements(dose_reference_sequence=[incomplete_item])
        
        result = prescription.check_required_elements()
        assert not result.is_valid
        assert result.has_errors
        assert result.error_count >= 3  # Should have errors for all 3 missing required elements
    
    def test_check_required_elements_with_complete_dose_reference(self):
        """Test check_required_elements with complete dose reference items."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        result = prescription.check_required_elements()
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_conditional_requirements_point_without_roi(self):
        """Test check_conditional_requirements for POINT type missing Referenced ROI Number."""
        # Create dose reference item with POINT type but missing Referenced ROI Number
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "POINT"
        item.DoseReferenceType = "TARGET"
        # Missing ReferencedROINumber - should trigger Type 1C validation error
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = prescription.check_conditional_requirements()
        assert not result.is_valid
        assert result.has_errors
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)
    
    def test_check_conditional_requirements_coordinates_without_coordinates(self):
        """Test check_conditional_requirements for COORDINATES type missing coordinates."""
        # Create dose reference item with COORDINATES type but missing coordinates
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "COORDINATES"
        item.DoseReferenceType = "TARGET"
        # Missing DoseReferencePointCoordinates - should trigger Type 1C validation error
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = prescription.check_conditional_requirements()
        assert not result.is_valid
        assert result.has_errors
        assert any("Dose Reference Point Coordinates (300A,0018) is required" in error for error in result.errors)
    
    def test_check_conditional_requirements_valid_conditions(self):
        """Test check_conditional_requirements with valid conditional requirements."""
        point_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        
        coordinates_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=2,
            dose_reference_structure_type=DoseReferenceStructureType.COORDINATES,
            dose_reference_type=DoseReferenceType.TARGET,
            dose_reference_point_coordinates=[100.0, 200.0, 300.0]
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[point_item, coordinates_item]
        )
        
        result = prescription.check_conditional_requirements()
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_enum_constraints_invalid_values(self):
        """Test check_enum_constraints with invalid enumerated values."""
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "INVALID_TYPE"  # Invalid enum value
        item.DoseReferenceType = "INVALID_REF_TYPE"      # Invalid enum value
        item.DoseValuePurpose = "INVALID_PURPOSE"        # Invalid enum value
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = prescription.check_enum_constraints()
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "Dose Reference Structure Type" in warning_text
        assert "Dose Reference Type" in warning_text
    
    def test_check_enum_constraints_valid_values(self):
        """Test check_enum_constraints with valid enumerated values."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1,
            dose_value_purpose=DoseValuePurpose.TRACKING,
            dose_value_interpretation=DoseValueInterpretation.ACTUAL
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        result = prescription.check_enum_constraints()
        assert not result.has_warnings
        assert result.is_valid
    
    def test_check_sequence_structures_duplicate_dose_reference_numbers(self):
        """Test check_sequence_structures with duplicate dose reference numbers."""
        item1 = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        
        item2 = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,  # Duplicate number
            dose_reference_structure_type=DoseReferenceStructureType.VOLUME,
            dose_reference_type=DoseReferenceType.ORGAN_AT_RISK,
            referenced_roi_number=2
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item1, item2]
        )
        
        result = prescription.check_sequence_structures()
        assert not result.is_valid
        assert result.has_errors
        assert any("must be unique within the RT Plan" in error for error in result.errors)
    
    def test_check_sequence_structures_invalid_coordinates_length(self):
        """Test check_sequence_structures with invalid coordinate length."""
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "COORDINATES"
        item.DoseReferenceType = "TARGET"
        item.DoseReferencePointCoordinates = [100.0, 200.0]  # Should have 3 values
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = prescription.check_sequence_structures()
        assert not result.is_valid
        assert result.has_errors
        assert any("must contain exactly 3 values" in error for error in result.errors)
    
    def test_check_dose_value_consistency_invalid_target_dose_ordering(self):
        """Test check_dose_value_consistency with invalid target dose ordering."""
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "POINT"
        item.DoseReferenceType = "TARGET"
        item.ReferencedROINumber = 1
        # Invalid dose ordering: min > prescription > max
        item.TargetMinimumDose = 300.0
        item.TargetPrescriptionDose = 200.0
        item.TargetMaximumDose = 100.0
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = prescription.check_dose_value_consistency()
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "should not exceed" in warning_text
    
    def test_check_dose_value_consistency_invalid_fraction_values(self):
        """Test check_dose_value_consistency with invalid fraction values."""
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "POINT"
        item.DoseReferenceType = "TARGET"
        item.ReferencedROINumber = 1
        item.TargetUnderdoseVolumeFraction = 150.0  # Should be 0-100
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = prescription.check_dose_value_consistency()
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "between 0.0 and 100.0 percent" in warning_text
    
    def test_private_validation_methods_success(self):
        """Test private validation methods with valid data."""
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        # These should not raise exceptions
        try:
            prescription._ensure_required_elements_valid()
            prescription._ensure_conditional_requirements_valid()
            prescription._ensure_enum_constraints_valid()
            prescription._ensure_sequence_structures_valid()
            prescription._ensure_dose_value_consistency_valid()
        except Exception as e:
            pytest.fail(f"Private validation methods should not raise exceptions with valid data: {e}")
    
    def test_private_validation_methods_failure(self):
        """Test private validation methods with invalid data."""
        # Create incomplete dose reference item
        item = Dataset()
        # Missing required elements
        
        prescription = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        # Should raise ValidationError
        with pytest.raises(ValidationError, match="Required elements validation failed"):
            prescription._ensure_required_elements_valid()
    
    def test_zero_copy_validation_integration(self):
        """Test that validation methods use zero-copy (pass self instead of dataset)."""
        # This test verifies the integration works, though we can't directly test
        # that self vs dataset is passed (that would require mocking)
        prescription = RTPrescriptionModule.from_required_elements()
        
        # All validation methods should work with the module instance directly
        result = prescription.validate()
        assert isinstance(result, ValidationResult)
        
        # Individual check methods should also work
        assert isinstance(prescription.check_required_elements(), ValidationResult)
        assert isinstance(prescription.check_conditional_requirements(), ValidationResult)
        assert isinstance(prescription.check_enum_constraints(), ValidationResult)
        assert isinstance(prescription.check_sequence_structures(), ValidationResult)
        assert isinstance(prescription.check_dose_value_consistency(), ValidationResult)