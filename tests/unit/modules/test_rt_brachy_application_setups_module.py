"""
Test RTBrachyApplicationSetupsModule functionality.

RTBrachyApplicationSetupsModule implements DICOM PS3.3 C.8.8.15
RT Brachy Application Setups Module using composition-based architecture.
"""

from pyrt_dicom.modules import RTBrachyApplicationSetupsModule
from pyrt_dicom.enums.rt_enums import (
    BrachyTreatmentTechnique, BrachyTreatmentType, ApplicationSetupType,
    SourceType, SourceMovementType, BrachyAccessoryDeviceType,
    SourceApplicatorType, SourceStrengthUnits
)
from pyrt_dicom.validators import ValidationResult
from pydicom import Dataset


class TestRTBrachyApplicationSetupsModule:
    """Test RTBrachyApplicationSetupsModule functionality with composition-based architecture."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create sample source sequence item
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )

        # Create sample treatment machine sequence item
        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        # Create sample application setup sequence item
        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )

        # Test dataset generation
        dataset = module.to_dataset()
        assert isinstance(dataset, Dataset)
        assert dataset.BrachyTreatmentTechnique == BrachyTreatmentTechnique.INTERSTITIAL.value
        assert dataset.BrachyTreatmentType == BrachyTreatmentType.HDR.value
        assert len(dataset.TreatmentMachineSequence) == 1
        assert len(dataset.SourceSequence) == 1
        assert len(dataset.ApplicationSetupSequence) == 1
    
    def test_required_elements_validation(self):
        """Test validation of required elements."""
        # Test minimal creation with required fields
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="",
            brachy_treatment_type="",
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Test dataset generation with empty values
        dataset = module.to_dataset()
        assert dataset.BrachyTreatmentTechnique == ""
        assert dataset.BrachyTreatmentType == ""
        assert len(dataset.TreatmentMachineSequence) == 0
        assert len(dataset.SourceSequence) == 0
        assert len(dataset.ApplicationSetupSequence) == 0
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        ).with_optional_elements()

        # Should return self for method chaining
        assert isinstance(module, RTBrachyApplicationSetupsModule)
    
    def test_create_source_item(self):
        """Test source item creation."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )

        assert source.SourceNumber == 1
        assert source.SourceType == SourceType.POINT.value
        assert source.SourceIsotopeName == "Ir-192"
        assert source.SourceIsotopeHalfLife == 73.8
        assert source.ReferenceAirKermaRate == 40800.0
        assert source.SourceStrengthReferenceDate == "20231201"
        assert source.SourceStrengthReferenceTime == "120000"
    
    def test_create_source_item_with_optional_elements(self):
        """Test source item creation with optional elements."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000",
            source_serial_number="SN123456",
            source_model_id="MODEL_A",
            source_description="HDR Source",
            source_manufacturer="ACME Corp"
        )

        assert source.SourceSerialNumber == "SN123456"
        assert source.SourceModelID == "MODEL_A"
        assert source.SourceDescription == "HDR Source"
        assert source.SourceManufacturer == "ACME Corp"
    
    def test_create_source_item_with_strength_units(self):
        """Test source item creation with source strength units for non-gamma sources."""
        source = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=2,
            source_type=SourceType.LINE,
            source_isotope_name="Sr-90",
            source_isotope_half_life=10585.0,
            reference_air_kerma_rate=0.0,  # Zero for non-gamma sources
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000",
            source_strength_units=SourceStrengthUnits.DOSE_RATE_WATER,
            source_strength=1000.0
        )

        assert source.SourceNumber == 2
        assert source.SourceType == SourceType.LINE.value
        assert source.SourceIsotopeName == "Sr-90"
        assert source.ReferenceAirKermaRate == 0.0
        assert source.SourceStrengthUnits == SourceStrengthUnits.DOSE_RATE_WATER.value
        assert source.SourceStrength == 1000.0
    
    def test_create_treatment_machine_item(self):
        """Test treatment machine item creation."""
        machine = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        assert machine.TreatmentMachineName == "HDR Unit 1"

    def test_create_treatment_machine_item_with_optional_elements(self):
        """Test treatment machine item creation with optional elements."""
        machine = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1",
            manufacturer="ACME Corp",
            institution_name="General Hospital",
            manufacturers_model_name="HDR-2000"
        )

        assert machine.TreatmentMachineName == "HDR Unit 1"
        assert machine.Manufacturer == "ACME Corp"
        assert machine.InstitutionName == "General Hospital"
        assert machine.ManufacturerModelName == "HDR-2000"
    
    def test_create_application_setup_item(self):
        """Test application setup item creation."""
        setup = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        assert setup.ApplicationSetupType == ApplicationSetupType.FLETCHER_SUIT.value
        assert setup.ApplicationSetupNumber == 1
        assert setup.TotalReferenceAirKerma == 0.0  # Default value

    def test_create_application_setup_item_with_optional_elements(self):
        """Test application setup item creation with optional elements."""
        setup = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            application_setup_name="Prostate Implant",
            total_reference_air_kerma=1000.0
        )

        assert setup.ApplicationSetupType == ApplicationSetupType.FLETCHER_SUIT.value
        assert setup.ApplicationSetupNumber == 1
        assert setup.ApplicationSetupName == "Prostate Implant"
        assert setup.TotalReferenceAirKerma == 1000.0
    
    def test_create_brachy_accessory_device_item(self):
        """Test brachy accessory device item creation."""
        accessory = RTBrachyApplicationSetupsModule.create_brachy_accessory_device_item(
            brachy_accessory_device_number=1,
            brachy_accessory_device_id="SHIELD_001",
            brachy_accessory_device_type=BrachyAccessoryDeviceType.SHIELD
        )

        assert accessory.BrachyAccessoryDeviceNumber == 1
        assert accessory.BrachyAccessoryDeviceID == "SHIELD_001"
        assert accessory.BrachyAccessoryDeviceType == BrachyAccessoryDeviceType.SHIELD.value
    
    def test_create_brachy_accessory_device_item_with_optional_elements(self):
        """Test brachy accessory device item creation with optional elements."""
        accessory = RTBrachyApplicationSetupsModule.create_brachy_accessory_device_item(
            brachy_accessory_device_number=2,
            brachy_accessory_device_id="MOLD_002",
            brachy_accessory_device_type=BrachyAccessoryDeviceType.MOLD,
            brachy_accessory_device_name="Custom Mold",
            material_id="MAT_001",
            brachy_accessory_device_nominal_thickness=2.5,
            brachy_accessory_device_nominal_transmission=0.95,
            referenced_roi_number=5
        )

        assert accessory.BrachyAccessoryDeviceNumber == 2
        assert accessory.BrachyAccessoryDeviceID == "MOLD_002"
        assert accessory.BrachyAccessoryDeviceType == BrachyAccessoryDeviceType.MOLD.value
        assert accessory.BrachyAccessoryDeviceName == "Custom Mold"
        assert accessory.MaterialID == "MAT_001"
        assert accessory.BrachyAccessoryDeviceNominalThickness == 2.5
        assert accessory.BrachyAccessoryDeviceNominalTransmission == 0.95
        assert accessory.ReferencedROINumber == 5
    
    def test_create_channel_item(self):
        """Test channel item creation with source movement type."""
        # Create a simple control point for the sequence
        control_point = Dataset()
        control_point.ControlPointIndex = 0
        control_point.ControlPointRelativePosition = 0.0
        control_point.CumulativeTimeWeight = 0.0
        
        channel = RTBrachyApplicationSetupsModule.create_channel_item(
            channel_number=1,
            channel_total_time=300.0,
            source_movement_type=SourceMovementType.STEPWISE,
            referenced_source_number=1,
            number_of_control_points=1,
            brachy_control_point_sequence=[control_point]
        )

        assert channel.ChannelNumber == 1
        assert channel.ChannelTotalTime == 300.0
        assert channel.SourceMovementType == SourceMovementType.STEPWISE.value
        assert channel.ReferencedSourceNumber == 1
        assert channel.NumberOfControlPoints == 1
        assert len(channel.BrachyControlPointSequence) == 1
    
    def test_create_channel_item_with_source_applicator(self):
        """Test channel item creation with source applicator type."""
        # Create a simple control point for the sequence
        control_point = Dataset()
        control_point.ControlPointIndex = 0
        control_point.ControlPointRelativePosition = 0.0
        control_point.CumulativeTimeWeight = 0.0
        
        channel = RTBrachyApplicationSetupsModule.create_channel_item(
            channel_number=2,
            channel_total_time=600.0,
            source_movement_type=SourceMovementType.FIXED,
            referenced_source_number=1,
            number_of_control_points=1,
            brachy_control_point_sequence=[control_point],
            source_applicator_number=1,
            source_applicator_id="APP_001",
            source_applicator_type=SourceApplicatorType.FLEXIBLE,
            source_applicator_name="Flexible Catheter",
            source_applicator_length=150.0,
            source_applicator_manufacturer="ACME Corp"
        )

        assert channel.ChannelNumber == 2
        assert channel.ChannelTotalTime == 600.0
        assert channel.SourceMovementType == SourceMovementType.FIXED.value
        assert channel.ReferencedSourceNumber == 1
        assert channel.NumberOfControlPoints == 1
        assert channel.SourceApplicatorNumber == 1
        assert channel.SourceApplicatorID == "APP_001"
        assert channel.SourceApplicatorType == SourceApplicatorType.FLEXIBLE.value
        assert channel.SourceApplicatorName == "Flexible Catheter"
        assert channel.SourceApplicatorLength == 150.0
        assert channel.SourceApplicatorManufacturer == "ACME Corp"
    
    def test_has_application_setups_property(self):
        """Test has_application_setups property."""
        # Test without setup sequence
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Empty sequence should return False
        assert not module.has_application_setups

        # Add a setup and test again
        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup_item]
        )
        assert module.has_application_setups
    
    def test_application_setup_count_property(self):
        """Test application_setup_count property."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Initially no setups
        assert module.application_setup_count == 0

        # Add some setups
        setup1 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        setup2 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.MANCHESTER,
            application_setup_number=2
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup1, setup2]
        )
        assert module.application_setup_count == 2
    
    def test_get_application_setup_numbers(self):
        """Test get_application_setup_numbers method."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Initially no setup numbers
        assert module.get_application_setup_numbers() == []

        # Add some setups
        setup1 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        setup2 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.MANCHESTER,
            application_setup_number=3
        )
        setup3 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.HENSCHKE,
            application_setup_number=2
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup1, setup2, setup3]
        )
        numbers = module.get_application_setup_numbers()
        assert len(numbers) == 3
        assert 1 in numbers
        assert 2 in numbers
        assert 3 in numbers
    
    def test_get_application_setup_by_number(self):
        """Test get_application_setup_by_number method."""
        # Add some setups
        setup1 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            application_setup_name="Setup 1"
        )
        setup2 = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.MANCHESTER,
            application_setup_number=2,
            application_setup_name="Setup 2"
        )
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[setup1, setup2]
        )

        # Test finding existing setup
        found_setup = module.get_application_setup_by_number(1)
        assert found_setup is not None
        assert found_setup.ApplicationSetupName == 'Setup 1'

        # Test finding non-existent setup
        not_found = module.get_application_setup_by_number(99)
        assert not_found is None
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        assert hasattr(module, 'validate')
        assert callable(module.validate)

        # Test validation result structure
        validation_result = module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_new_properties(self):
        """Test new properties added with composition-based architecture."""
        # Create a module with sources and treatment machine
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20231201",
            source_strength_reference_time="120000"
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )

        # Test properties
        assert module.has_sources
        assert module.source_count == 1
        assert module.has_treatment_machine
        assert module.is_configured
        assert not module.is_pdr_treatment
        assert not module.is_permanent_implant

        # Test source-related methods
        assert module.get_source_numbers() == [1]
        assert module.get_source_by_number(1) is not None
        assert module.get_source_by_number(99) is None

    def test_pdr_treatment_detection(self):
        """Test PDR treatment detection."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.PDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        assert module.is_pdr_treatment

    def test_permanent_implant_detection(self):
        """Test permanent implant detection."""
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.PERMANENT,
            brachy_treatment_type=BrachyTreatmentType.LDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        assert module.is_permanent_implant

    def test_validate_calls_validator_with_zero_copy(self):
        """Test that validate method calls the validator with zero-copy (self)."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        with patch('pyrt_dicom.validators.modules.rt_brachy_application_setups_validator.RTBrachyApplicationSetupsValidator.validate') as mock_validate:
            mock_result = ValidationResult()
            mock_validate.return_value = mock_result

            from pyrt_dicom.validators.modules.base_validator import ValidationConfig
            config = ValidationConfig()
            result = module.validate(config)

            mock_validate.assert_called_once_with(module, config)
            assert result is mock_result

    def test_check_required_elements(self):
        """Test check_required_elements method."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        with patch('pyrt_dicom.validators.modules.rt_brachy_application_setups_validator.RTBrachyApplicationSetupsValidator.validate_required_elements') as mock_validate:
            mock_result = ValidationResult()
            mock_validate.return_value = mock_result

            result = module.check_required_elements()

            mock_validate.assert_called_once_with(module)
            assert result is mock_result

    def test_check_conditional_requirements(self):
        """Test check_conditional_requirements method."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        with patch('pyrt_dicom.validators.modules.rt_brachy_application_setups_validator.RTBrachyApplicationSetupsValidator.validate_conditional_requirements') as mock_validate:
            mock_result = ValidationResult()
            mock_validate.return_value = mock_result

            result = module.check_conditional_requirements()

            mock_validate.assert_called_once_with(module)
            assert result is mock_result

    def test_check_enum_constraints(self):
        """Test check_enum_constraints method."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        with patch('pyrt_dicom.validators.modules.rt_brachy_application_setups_validator.RTBrachyApplicationSetupsValidator.validate_enumerated_values') as mock_validate:
            mock_result = ValidationResult()
            mock_validate.return_value = mock_result

            result = module.check_enum_constraints()

            mock_validate.assert_called_once_with(module)
            assert result is mock_result

    def test_check_sequence_requirements(self):
        """Test check_sequence_requirements method."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        with patch('pyrt_dicom.validators.modules.rt_brachy_application_setups_validator.RTBrachyApplicationSetupsValidator.validate_sequence_structures') as mock_validate:
            mock_result = ValidationResult()
            mock_validate.return_value = mock_result

            result = module.check_sequence_requirements()

            mock_validate.assert_called_once_with(module)
            assert result is mock_result

    def test_ensure_required_elements_valid_success(self):
        """Test _ensure_required_elements_valid with valid data."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Patch the check_required_elements method to return a valid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_required_elements', return_value=ValidationResult()) as mock_check:
            # Should not raise exception
            module._ensure_required_elements_valid()
            mock_check.assert_called_once()

    def test_ensure_required_elements_valid_failure(self):
        """Test _ensure_required_elements_valid with invalid data."""
        import pytest
        from unittest.mock import patch
        from pyrt_dicom.validators.validation_error import ValidationError

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Create an invalid result
        invalid_result = ValidationResult()
        invalid_result.add_error("Test error")

        # Patch the check_required_elements method to return an invalid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_required_elements', return_value=invalid_result):
            with pytest.raises(ValidationError, match="Required elements validation failed"):
                module._ensure_required_elements_valid()

    def test_ensure_conditional_requirements_valid_success(self):
        """Test _ensure_conditional_requirements_valid with valid data."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Patch the check_conditional_requirements method to return a valid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_conditional_requirements', return_value=ValidationResult()) as mock_check:
            # Should not raise exception
            module._ensure_conditional_requirements_valid()
            mock_check.assert_called_once()

    def test_ensure_conditional_requirements_valid_failure(self):
        """Test _ensure_conditional_requirements_valid with invalid data."""
        import pytest
        from unittest.mock import patch
        from pyrt_dicom.validators.validation_error import ValidationError

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Create an invalid result
        invalid_result = ValidationResult()
        invalid_result.add_error("Test error")

        # Patch the check_conditional_requirements method to return an invalid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_conditional_requirements', return_value=invalid_result):
            with pytest.raises(ValidationError, match="Conditional requirements validation failed"):
                module._ensure_conditional_requirements_valid()

    def test_ensure_enum_constraints_valid_success(self):
        """Test _ensure_enum_constraints_valid with valid data."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Patch the check_enum_constraints method to return a valid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_enum_constraints', return_value=ValidationResult()) as mock_check:
            # Should not raise exception
            module._ensure_enum_constraints_valid()
            mock_check.assert_called_once()

    def test_ensure_enum_constraints_valid_failure(self):
        """Test _ensure_enum_constraints_valid with invalid data."""
        import pytest
        from unittest.mock import patch
        from pyrt_dicom.validators.validation_error import ValidationError

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Create an invalid result
        invalid_result = ValidationResult()
        invalid_result.add_error("Test error")

        # Patch the check_enum_constraints method to return an invalid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_enum_constraints', return_value=invalid_result):
            with pytest.raises(ValidationError, match="Enumerated value constraints validation failed"):
                module._ensure_enum_constraints_valid()

    def test_ensure_sequence_requirements_valid_success(self):
        """Test _ensure_sequence_requirements_valid with valid data."""
        from unittest.mock import patch

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Patch the check_sequence_requirements method to return a valid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_sequence_requirements', return_value=ValidationResult()) as mock_check:
            # Should not raise exception
            module._ensure_sequence_requirements_valid()
            mock_check.assert_called_once()

    def test_ensure_sequence_requirements_valid_failure(self):
        """Test _ensure_sequence_requirements_valid with invalid data."""
        import pytest
        from unittest.mock import patch
        from pyrt_dicom.validators.validation_error import ValidationError

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[],
            source_sequence=[],
            application_setup_sequence=[]
        )

        # Create an invalid result
        invalid_result = ValidationResult()
        invalid_result.add_error("Test error")

        # Patch the check_sequence_requirements method to return an invalid result
        with patch.object(RTBrachyApplicationSetupsModule, 'check_sequence_requirements', return_value=invalid_result):
            with pytest.raises(ValidationError, match="Sequence requirements validation failed"):
                module._ensure_sequence_requirements_valid()