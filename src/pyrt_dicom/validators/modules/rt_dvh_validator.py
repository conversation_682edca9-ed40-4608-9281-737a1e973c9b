"""RT DVH Module DICOM validation - PS3.3 C.8.8.4

The RT DVH Module provides for the inclusion of differential or cumulative
dose volume histogram data. This validator ensures compliance with DICOM
PS3.3 Section C.8.8.4 requirements.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.dose_enums import DoseUnits, DoseType
from ...enums.rt_enums import DVHType, DVHROIContributionType, DVHVolumeUnits

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTDVHValidator(BaseValidator):
    """Validator for DICOM RT DVH Module (PS3.3 C.8.8.4).

    Validates dose-volume histogram data including:
    - Type 1 required elements (Referenced Structure Set Sequence, DVH Sequence)
    - Type 3 optional elements (DVH Normalization Point, DVH Normalization Dose Value)
    - Conditional requirements for RELATIVE dose units
    - DVH data consistency and logical constraints
    - Enumerated value compliance
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 (required) elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Referenced Structure Set Sequence (300C,0060) - Type 1
        if 'ReferencedStructureSetSequence' not in data:
            result.add_error(
                "Referenced Structure Set Sequence (300C,0060) is required (Type 1). "
                "This sequence describes the Structure Set containing structures used to calculate DVHs."
            )
        else:
            ref_seq = data.ReferencedStructureSetSequence
            if len(ref_seq) == 0:
                result.add_error(
                    "Referenced Structure Set Sequence (300C,0060) is required (Type 1). "
                    "This sequence describes the Structure Set containing structures used to calculate DVHs."
                )
            elif len(ref_seq) != 1:
                result.add_error(
                    f"Referenced Structure Set Sequence (300C,0060) must contain exactly one item, "
                    f"found {len(ref_seq)} items. Only a single Structure Set reference is allowed."
                )

        # DVH Sequence (3004,0050) - Type 1
        if 'DVHSequence' not in data:
            result.add_error(
                "DVH Sequence (3004,0050) is required (Type 1). "
                "One or more DVH items must be included in this sequence."
            )
        else:
            dvh_seq = data.DVHSequence
            if len(dvh_seq) == 0:
                result.add_error(
                    "DVH Sequence (3004,0050) is required (Type 1). "
                    "One or more DVH items must be included in this sequence."
                )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        dvh_sequence = data.DVHSequence if 'DVHSequence' in data else []
        dvh_norm_dose = data.DVHNormalizationDoseValue if 'DVHNormalizationDoseValue' in data else None

        # Check if any DVH uses RELATIVE dose units
        has_relative_dose = any(
            dvh_item.get('DoseUnits', '') == DoseUnits.RELATIVE.value
            for dvh_item in dvh_sequence
        )

        # DVH Normalization Dose Value should be present when RELATIVE dose units are used
        if has_relative_dose and dvh_norm_dose is None:
            result.add_warning(
                "DVH Normalization Dose Value (3004,0042) should be present when any DVH uses "
                "RELATIVE dose units. This value serves as the reference for relative dose calculations. "
                "Consider adding this element for proper DVH interpretation."
            )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        dvh_sequence = data.DVHSequence if 'DVHSequence' in data else []
        for i, dvh_item in enumerate(dvh_sequence):
            # DVH Type (3004,0001) - Type 1
            dvh_type = dvh_item.get('DVHType', '')
            if dvh_type:
                valid_types = [dtype.value for dtype in DVHType]
                BaseValidator.validate_enumerated_value(
                    dvh_type, valid_types,
                    f"DVH Type (3004,0001) in DVH Sequence item {i}. "
                    f"Valid values: {', '.join(valid_types)}", result
                )

            # Dose Units (3004,0002) - Type 1
            dose_units = dvh_item.get('DoseUnits', '')
            if dose_units:
                valid_units = [units.value for units in DoseUnits]
                BaseValidator.validate_enumerated_value(
                    dose_units, valid_units,
                    f"Dose Units (3004,0002) in DVH Sequence item {i}. "
                    f"Valid values: {', '.join(valid_units)}", result
                )

            # Dose Type (3004,0004) - Type 1
            dose_type = dvh_item.get('DoseType', '')
            if dose_type:
                valid_dose_types = [dtype.value for dtype in DoseType]
                BaseValidator.validate_enumerated_value(
                    dose_type, valid_dose_types,
                    f"Dose Type (3004,0004) in DVH Sequence item {i}. "
                    f"Valid values: {', '.join(valid_dose_types)}", result
                )

            # DVH Volume Units (3004,0054) - Type 1
            dvh_volume_units = dvh_item.get('DVHVolumeUnits', '')
            if dvh_volume_units:
                valid_volume_units = [units.value for units in DVHVolumeUnits]
                BaseValidator.validate_enumerated_value(
                    dvh_volume_units, valid_volume_units,
                    f"DVH Volume Units (3004,0054) in DVH Sequence item {i}. "
                    f"Valid values: {', '.join(valid_volume_units)}", result
                )

            # DVH ROI Contribution Type in DVH Referenced ROI Sequence
            dvh_roi_seq = dvh_item.get('DVHReferencedROISequence', [])
            for j, roi_item in enumerate(dvh_roi_seq):
                contribution_type = roi_item.get('DVHROIContributionType', '')
                if contribution_type:
                    valid_contribution_types = [ctype.value for ctype in DVHROIContributionType]
                    BaseValidator.validate_enumerated_value(
                        contribution_type, valid_contribution_types,
                        f"DVH ROI Contribution Type (3004,0062) in DVH Sequence item {i}, "
                        f"ROI item {j}. Valid values: {', '.join(valid_contribution_types)}", result
                    )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements and nested elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Referenced Structure Set Sequence validation
        ref_structure_set_seq = data.ReferencedStructureSetSequence if 'ReferencedStructureSetSequence' in data else []
        for i, struct_item in enumerate(ref_structure_set_seq):
            if not struct_item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Structure Set Sequence item {i}: "
                    f"Referenced SOP Class UID (0008,1150) is required (Type 1). "
                    f"This should reference the RT Structure Set SOP Class UID."
                )
            if not struct_item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Structure Set Sequence item {i}: "
                    f"Referenced SOP Instance UID (0008,1155) is required (Type 1). "
                    f"This should reference the specific RT Structure Set instance."
                )

        # DVH Sequence validation
        dvh_sequence = data.DVHSequence if 'DVHSequence' in data else []
        for i, dvh_item in enumerate(dvh_sequence):
            RTDVHValidator._validate_dvh_sequence_item(dvh_item, i, result)

        return result

    @staticmethod
    def validate_data_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate DVH data consistency and logical constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid data consistency
        """
        result = ValidationResult()

        dvh_sequence = data.DVHSequence if 'DVHSequence' in data else []
        for i, dvh_item in enumerate(dvh_sequence):
            RTDVHValidator._validate_dvh_data_format(dvh_item, i, result)
            RTDVHValidator._validate_dvh_dose_scaling(dvh_item, i, result)
            RTDVHValidator._validate_dvh_statistical_consistency(dvh_item, i, result)

        # Validate normalization point coordinates if present
        if 'DVHNormalizationPoint' in data:
            dvh_norm_point = data.DVHNormalizationPoint
            if not isinstance(dvh_norm_point, (list, tuple)) or len(dvh_norm_point) != 3:
                result.add_error(
                    "DVH Normalization Point (3004,0040) must contain exactly 3 coordinates (x, y, z) "
                    "in the Patient-Based Coordinate System (mm)."
                )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(RTDVHValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(RTDVHValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(RTDVHValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(RTDVHValidator.validate_sequence_structures(data))

        # Always validate data consistency
        result.merge(RTDVHValidator.validate_data_consistency(data))

        return result

    @staticmethod
    def _validate_dvh_sequence_item(dvh_item: Dataset, item_index: int, result: ValidationResult) -> None:
        """Validate individual DVH sequence item requirements."""

        # DVH Referenced ROI Sequence validation (Type 1)
        dvh_roi_seq = dvh_item.get('DVHReferencedROISequence', [])
        if not dvh_roi_seq:
            result.add_error(
                f"DVH Sequence item {item_index}: "
                f"DVH Referenced ROI Sequence (3004,0060) is required (Type 1). "
                f"One or more ROI references must be included."
            )

        for j, roi_item in enumerate(dvh_roi_seq):
            if not roi_item.get('ReferencedROINumber'):
                result.add_error(
                    f"DVH Referenced ROI Sequence item {j} in DVH Sequence item {item_index}: "
                    f"Referenced ROI Number (3006,0084) is required (Type 1). "
                    f"This must reference a valid ROI from the Structure Set."
                )
            if not roi_item.get('DVHROIContributionType'):
                result.add_error(
                    f"DVH Referenced ROI Sequence item {j} in DVH Sequence item {item_index}: "
                    f"DVH ROI Contribution Type (3004,0062) is required (Type 1). "
                    f"Specify whether the ROI volume is INCLUDED or EXCLUDED in the DVH calculation."
                )

        # Required DVH elements validation (Type 1)
        required_dvh_elements = [
            ('DVHType', '3004,0001', 'Type of DVH (DIFFERENTIAL, CUMULATIVE, or NATURAL)'),
            ('DoseUnits', '3004,0002', 'Dose axis units (GY or RELATIVE)'),
            ('DoseType', '3004,0004', 'Type of dose (PHYSICAL, EFFECTIVE, or ERROR)'),
            ('DVHDoseScaling', '3004,0052', 'Scaling factor for dose bin widths'),
            ('DVHVolumeUnits', '3004,0054', 'Volume axis units (CM3, PERCENT, or PER_U)'),
            ('DVHNumberOfBins', '3004,0056', 'Number of bins used to store DVH Data'),
            ('DVHData', '3004,0058', 'Data stream describing dose bin widths and volumes')
        ]

        for element_name, tag, description in required_dvh_elements:
            if not dvh_item.get(element_name):
                result.add_error(
                    f"DVH Sequence item {item_index}: "
                    f"{element_name} ({tag}) is required (Type 1). {description}."
                )
        

    @staticmethod
    def _validate_dvh_data_format(dvh_item: Dataset, item_index: int, result: ValidationResult) -> None:
        """Validate DVH data format and length consistency."""

        dvh_number_of_bins = dvh_item.get('DVHNumberOfBins', 0)
        dvh_data = dvh_item.get('DVHData', [])

        if dvh_number_of_bins and dvh_data:
            expected_data_length = dvh_number_of_bins * 2  # Each bin has dose and volume
            if len(dvh_data) != expected_data_length:
                result.add_error(
                    f"DVH Sequence item {item_index}: "
                    f"DVH Data (3004,0058) length ({len(dvh_data)}) must be exactly twice the "
                    f"Number of Bins ({dvh_number_of_bins}). Format: D1V1, D2V2, ..., DnVn "
                    f"where Dn are dose bin widths and Vn are associated volumes."
                )

        # Validate that DVH data contains numeric values
        if dvh_data:
            try:
                # Attempt to convert to float to ensure numeric data
                numeric_data = [float(x) for x in dvh_data]
                # Check for negative volumes (odd indices are volumes)
                for j in range(1, len(numeric_data), 2):
                    if numeric_data[j] < 0:
                        result.add_warning(
                            f"DVH Sequence item {item_index}: "
                            f"Volume value at position {j} is negative ({numeric_data[j]}). "
                            f"DVH volumes should typically be non-negative."
                        )
            except (ValueError, TypeError):
                result.add_error(
                    f"DVH Sequence item {item_index}: "
                    f"DVH Data (3004,0058) must contain numeric values only."
                )

    @staticmethod
    def _validate_dvh_dose_scaling(dvh_item: Dataset, item_index: int, result: ValidationResult) -> None:
        """Validate DVH dose scaling value."""

        dvh_dose_scaling = dvh_item.get('DVHDoseScaling')
        if dvh_dose_scaling is not None:
            try:
                scaling_value = float(dvh_dose_scaling)
                if scaling_value <= 0:
                    result.add_error(
                        f"DVH Sequence item {item_index}: "
                        f"DVH Dose Scaling (3004,0052) must be positive, found {scaling_value}. "
                        f"This scaling factor is multiplied by dose bin widths to yield dose units."
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"DVH Sequence item {item_index}: "
                    f"DVH Dose Scaling (3004,0052) must be a numeric value."
                )

    @staticmethod
    def _validate_dvh_statistical_consistency(dvh_item: Dataset, item_index: int, result: ValidationResult) -> None:
        """Validate DVH statistical parameters consistency."""

        dvh_min_dose = dvh_item.get('DVHMinimumDose')
        dvh_max_dose = dvh_item.get('DVHMaximumDose')
        dvh_mean_dose = dvh_item.get('DVHMeanDose')

        # Convert to float for comparison if present
        try:
            min_dose = float(dvh_min_dose) if dvh_min_dose is not None else None
            max_dose = float(dvh_max_dose) if dvh_max_dose is not None else None
            mean_dose = float(dvh_mean_dose) if dvh_mean_dose is not None else None

            if min_dose is not None and max_dose is not None:
                if min_dose > max_dose:
                    result.add_error(
                        f"DVH Sequence item {item_index}: "
                        f"DVH Minimum Dose ({min_dose}) cannot exceed DVH Maximum Dose ({max_dose}). "
                        f"Check dose calculation consistency."
                    )

                if mean_dose is not None:
                    if mean_dose < min_dose or mean_dose > max_dose:
                        result.add_warning(
                            f"DVH Sequence item {item_index}: "
                            f"DVH Mean Dose ({mean_dose}) should be between minimum ({min_dose}) "
                            f"and maximum ({max_dose}) doses. Verify dose statistics calculation."
                        )
        except (ValueError, TypeError):
            result.add_warning(
                f"DVH Sequence item {item_index}: "
                f"DVH statistical dose values (min/max/mean) should be numeric."
            )
