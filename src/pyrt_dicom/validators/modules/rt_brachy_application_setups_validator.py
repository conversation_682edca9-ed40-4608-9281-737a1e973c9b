"""
RT Brachy Application Setups Module DICOM validation - PS3.3 C.8.8.15

This validator implements comprehensive validation for the RT Brachy Application Setups Module
according to DICOM PS3.3 C.8.8.15 specification, including:
- Type 1/1C/2/2C/3 element validation
- Enumerated value validation
- Conditional logic validation
- Semantic consistency validation
- Cross-reference validation
"""

from datetime import datetime
from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import (
    BrachyTreatmentTechnique, BrachyTreatmentType, ApplicationSetupType,
    SourceType, SourceMovementType, BrachyAccessoryDeviceType,
    SourceApplicatorType, SourceStrengthUnits
)

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTBrachyApplicationSetupsValidator(BaseValidator):
    """
    Validator for DICOM RT Brachy Application Setups Module (PS3.3 C.8.8.15).

    This validator provides comprehensive validation including:
    - Required element presence validation (Type 1)
    - Conditional element validation (Type 1C/2C)
    - Enumerated value validation
    - Semantic consistency validation
    - Cross-reference validation
    - Value range validation

    Independent validator that works on pydicom Dataset OR BaseModule instances.
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Brachy Treatment Technique (300A,0200) - Type 1
        if 'BrachyTreatmentTechnique' not in data or not data.BrachyTreatmentTechnique:
            result.add_error(
                "BrachyTreatmentTechnique (300A,0200) is required (Type 1) and must have a value"
            )

        # Brachy Treatment Type (300A,0202) - Type 1
        if 'BrachyTreatmentType' not in data or not data.BrachyTreatmentType:
            result.add_error(
                "BrachyTreatmentType (300A,0202) is required (Type 1) and must have a value"
            )

        # Treatment Machine Sequence (300A,0206) - Type 1
        # Only a single Item shall be included in this Sequence
        if 'TreatmentMachineSequence' not in data:
            result.add_error(
                "TreatmentMachineSequence (300A,0206) is required (Type 1)"
            )
        else:
            treatment_machine_seq = data.TreatmentMachineSequence
            if len(treatment_machine_seq) == 0:
                result.add_error(
                    "TreatmentMachineSequence (300A,0206) is required (Type 1)"
                )
            elif len(treatment_machine_seq) != 1:
                result.add_error(
                    f"TreatmentMachineSequence (300A,0206) must contain exactly one item, found {len(treatment_machine_seq)}"
                )
            else:
                machine_item = treatment_machine_seq[0]
                # Treatment Machine Name (300A,00B2) - Type 2
                if 'TreatmentMachineName' not in machine_item:
                    result.add_error(
                        "TreatmentMachineName (300A,00B2) is required (Type 2) in Treatment Machine Sequence"
                    )

        # Source Sequence (300A,0210) - Type 1
        # One or more Items shall be included in this Sequence
        if 'SourceSequence' not in data:
            result.add_error(
                "SourceSequence (300A,0210) is required (Type 1)"
            )
        elif len(data.SourceSequence) == 0:
            result.add_error(
                "SourceSequence (300A,0210) is required (Type 1)"
            )

        # Application Setup Sequence (300A,0230) - Type 1
        # One or more Items shall be included in this Sequence
        if 'ApplicationSetupSequence' not in data:
            result.add_error(
                "ApplicationSetupSequence (300A,0230) is required (Type 1)"
            )
        elif len(data.ApplicationSetupSequence) == 0:
            result.add_error(
                "ApplicationSetupSequence (300A,0230) is required (Type 1)"
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Check if this is a PDR treatment for conditional validation
        is_pdr_treatment = ('BrachyTreatmentType' in data and
                           data.BrachyTreatmentType == BrachyTreatmentType.PDR.value)

        # Validate Source Sequence conditional elements
        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            for i, source_item in enumerate(source_seq):
                # Source Strength Units (300A,0229) - Type 1C
                # Required if the source is not a gamma-emitting (photon) source
                has_source_strength_units = 'SourceStrengthUnits' in source_item
                has_source_strength = 'SourceStrength' in source_item

                # Source Strength (300A,022B) - Type 1C
                # Required if the source is not a gamma-emitting (photon) source
                if has_source_strength_units and not has_source_strength:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrength (300A,022B) is required (Type 1C) "
                        "when SourceStrengthUnits is present"
                    )
                elif has_source_strength and not has_source_strength_units:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrengthUnits (300A,0229) is required (Type 1C) "
                        "when SourceStrength is present"
                    )

        # Validate Application Setup Sequence conditional elements
        if 'ApplicationSetupSequence' in data:
            app_setup_seq = data.ApplicationSetupSequence
            for i, setup_item in enumerate(app_setup_seq):
                # Validate Channel Sequence conditional elements
                if 'ChannelSequence' in setup_item:
                    channel_seq = setup_item.ChannelSequence
                    for j, channel_item in enumerate(channel_seq):
                        # Number of Pulses (300A,028A) - Type 1C
                        # Required if Brachy Treatment Type is PDR
                        if is_pdr_treatment and 'NumberOfPulses' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfPulses (300A,028A) "
                                "is required (Type 1C) when Brachy Treatment Type is PDR"
                            )

                        # Pulse Repetition Interval (300A,028C) - Type 1C
                        # Required if Brachy Treatment Type is PDR
                        if is_pdr_treatment and 'PulseRepetitionInterval' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].PulseRepetitionInterval (300A,028C) "
                                "is required (Type 1C) when Brachy Treatment Type is PDR"
                            )

                        # Source Applicator Step Size (300A,02A0) - Type 1C
                        # Required if Source Movement Type is STEPWISE
                        if ('SourceMovementType' in channel_item and
                            channel_item.SourceMovementType == SourceMovementType.STEPWISE.value and
                            'SourceApplicatorStepSize' not in channel_item):
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorStepSize (300A,02A0) "
                                "is required (Type 1C) when Source Movement Type is STEPWISE"
                            )

                        # Source Applicator ID (300A,0291) - Type 2C
                        # Required if Source Applicator Number is present
                        has_applicator_number = 'SourceApplicatorNumber' in channel_item
                        if has_applicator_number and 'SourceApplicatorID' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorID (300A,0291) "
                                "is required (Type 2C) when Source Applicator Number is present"
                            )

                        # Source Applicator Type (300A,0292) - Type 1C
                        # Required if Source Applicator Number is present
                        if has_applicator_number and 'SourceApplicatorType' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorType (300A,0292) "
                                "is required (Type 1C) when Source Applicator Number is present"
                            )

                        # Source Applicator Length (300A,0296) - Type 1C
                        # Required if Source Applicator Number is present
                        if has_applicator_number and 'SourceApplicatorLength' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorLength (300A,0296) "
                                "is required (Type 1C) when Source Applicator Number is present"
                            )

                        # Source Applicator Tip Length (300A,0274) - Type 2C
                        # Required if Channel Effective Length is present
                        has_effective_length = 'ChannelEffectiveLength' in channel_item
                        if has_effective_length and 'SourceApplicatorTipLength' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorTipLength (300A,0274) "
                                "is required (Type 2C) when Channel Effective Length is present"
                            )

                        # Channel Inner Length (300A,0272) - Type 2C
                        # Required if Channel Effective Length is present
                        if has_effective_length and 'ChannelInnerLength' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelInnerLength (300A,0272) "
                                "is required (Type 2C) when Channel Effective Length is present"
                            )

                        # Referenced ROI Number (3006,0084) - Type 2C
                        # Required if Source Applicator Number is present
                        if has_applicator_number and 'ReferencedROINumber' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ReferencedROINumber (3006,0084) "
                                "is required (Type 2C) when Source Applicator Number is present"
                            )

                        # Transfer Tube Length (300A,02A4) - Type 2C
                        # Required if Transfer Tube Number is non-null
                        has_transfer_tube = ('TransferTubeNumber' in channel_item and
                                           channel_item.TransferTubeNumber is not None)
                        if has_transfer_tube and 'TransferTubeLength' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].TransferTubeLength (300A,02A4) "
                                "is required (Type 2C) when Transfer Tube Number is present"
                            )

                        # Final Cumulative Time Weight (300A,02C8) - Type 1C
                        # Required if Cumulative Time Weight is non-null in Control Points
                        if 'BrachyControlPointSequence' in channel_item:
                            control_points = channel_item.BrachyControlPointSequence
                            has_cumulative_time_weight = any(
                                'CumulativeTimeWeight' in cp for cp in control_points
                            )
                            if has_cumulative_time_weight and 'FinalCumulativeTimeWeight' not in channel_item:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].FinalCumulativeTimeWeight (300A,02C8) "
                                    "is required (Type 1C) when Cumulative Time Weight is present in Control Points"
                                )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Brachy Treatment Technique (300A,0200) - Enumerated Values
        if 'BrachyTreatmentTechnique' in data:
            technique = data.BrachyTreatmentTechnique
            valid_techniques = [e.value for e in BrachyTreatmentTechnique]
            if technique not in valid_techniques:
                result.add_error(
                    f"BrachyTreatmentTechnique (300A,0200) value '{technique}' is not a valid enumerated value. "
                    f"Valid values: {valid_techniques}"
                )

        # Brachy Treatment Type (300A,0202) - Defined Terms
        if 'BrachyTreatmentType' in data:
            treatment_type = data.BrachyTreatmentType
            valid_types = [e.value for e in BrachyTreatmentType]
            if treatment_type not in valid_types:
                result.add_error(
                    f"BrachyTreatmentType (300A,0202) value '{treatment_type}' is not a valid defined term. "
                    f"Valid values: {valid_types}"
                )

        # Validate Source Sequence enumerated values
        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            for i, source_item in enumerate(source_seq):
                # Source Type (300A,0214) - Defined Terms
                if 'SourceType' in source_item:
                    source_type = source_item.SourceType
                    valid_source_types = [e.value for e in SourceType]
                    if source_type not in valid_source_types:
                        result.add_error(
                            f"SourceSequence[{i}].SourceType (300A,0214) value '{source_type}' is not valid. "
                            f"Valid values: {valid_source_types}"
                        )

                # Source Strength Units (300A,0229) - Enumerated Values (if present)
                if 'SourceStrengthUnits' in source_item:
                    units = source_item.SourceStrengthUnits
                    valid_units = [e.value for e in SourceStrengthUnits]
                    if units not in valid_units:
                        result.add_error(
                            f"SourceSequence[{i}].SourceStrengthUnits (300A,0229) value '{units}' is not valid. "
                            f"Valid values: {valid_units}"
                        )

        # Validate Application Setup Sequence enumerated values
        if 'ApplicationSetupSequence' in data:
            app_setup_seq = data.ApplicationSetupSequence
            for i, setup_item in enumerate(app_setup_seq):
                # Application Setup Type (300A,0232) - Defined Terms
                if 'ApplicationSetupType' in setup_item:
                    setup_type = setup_item.ApplicationSetupType
                    valid_setup_types = [e.value for e in ApplicationSetupType]
                    if setup_type not in valid_setup_types:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ApplicationSetupType (300A,0232) value '{setup_type}' is not valid. "
                            f"Valid values: {valid_setup_types}"
                        )

                # Validate Brachy Accessory Device Sequence enumerated values
                if 'BrachyAccessoryDeviceSequence' in setup_item:
                    accessory_seq = setup_item.BrachyAccessoryDeviceSequence
                    for j, accessory_item in enumerate(accessory_seq):
                        if 'BrachyAccessoryDeviceType' in accessory_item:
                            device_type = accessory_item.BrachyAccessoryDeviceType
                            valid_device_types = [e.value for e in BrachyAccessoryDeviceType]
                            if device_type not in valid_device_types:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].BrachyAccessoryDeviceSequence[{j}]."
                                    f"BrachyAccessoryDeviceType (300A,0264) value '{device_type}' is not valid. "
                                    f"Valid values: {valid_device_types}"
                                )

                # Validate Channel Sequence enumerated values
                if 'ChannelSequence' in setup_item:
                    channel_seq = setup_item.ChannelSequence
                    for j, channel_item in enumerate(channel_seq):
                        # Source Movement Type (300A,0288) - Defined Terms
                        if 'SourceMovementType' in channel_item:
                            movement_type = channel_item.SourceMovementType
                            valid_movement_types = [e.value for e in SourceMovementType]
                            if movement_type not in valid_movement_types:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}]."
                                    f"SourceMovementType (300A,0288) value '{movement_type}' is not valid. "
                                    f"Valid values: {valid_movement_types}"
                                )

                        # Source Applicator Type (300A,0292) - Defined Terms (if present)
                        if 'SourceApplicatorType' in channel_item:
                            applicator_type = channel_item.SourceApplicatorType
                            valid_applicator_types = [e.value for e in SourceApplicatorType]
                            if applicator_type not in valid_applicator_types:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}]."
                                    f"SourceApplicatorType (300A,0292) value '{applicator_type}' is not valid. "
                                    f"Valid values: {valid_applicator_types}"
                                )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Validate Source Sequence Type 1 elements
        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            for i, source_item in enumerate(source_seq):
                # Source Number (300A,0212) - Type 1
                if 'SourceNumber' not in source_item or source_item.SourceNumber is None:
                    result.add_error(
                        f"SourceSequence[{i}].SourceNumber (300A,0212) is required (Type 1)"
                    )

                # Source Type (300A,0214) - Type 1
                if 'SourceType' not in source_item or not source_item.SourceType:
                    result.add_error(
                        f"SourceSequence[{i}].SourceType (300A,0214) is required (Type 1)"
                    )

                # Source Isotope Name (300A,0226) - Type 1
                if 'SourceIsotopeName' not in source_item or not source_item.SourceIsotopeName:
                    result.add_error(
                        f"SourceSequence[{i}].SourceIsotopeName (300A,0226) is required (Type 1)"
                    )

                # Source Isotope Half Life (300A,0228) - Type 1
                if 'SourceIsotopeHalfLife' not in source_item or source_item.SourceIsotopeHalfLife is None:
                    result.add_error(
                        f"SourceSequence[{i}].SourceIsotopeHalfLife (300A,0228) is required (Type 1)"
                    )

                # Reference Air Kerma Rate (300A,022A) - Type 1
                if 'ReferenceAirKermaRate' not in source_item or source_item.ReferenceAirKermaRate is None:
                    result.add_error(
                        f"SourceSequence[{i}].ReferenceAirKermaRate (300A,022A) is required (Type 1)"
                    )

                # Source Strength Reference Date (300A,022C) - Type 1
                if 'SourceStrengthReferenceDate' not in source_item or not source_item.SourceStrengthReferenceDate:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrengthReferenceDate (300A,022C) is required (Type 1)"
                    )

                # Source Strength Reference Time (300A,022E) - Type 1
                if 'SourceStrengthReferenceTime' not in source_item or not source_item.SourceStrengthReferenceTime:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrengthReferenceTime (300A,022E) is required (Type 1)"
                    )

        # Validate Application Setup Sequence Type 1 elements
        if 'ApplicationSetupSequence' in data:
            app_setup_seq = data.ApplicationSetupSequence
            for i, setup_item in enumerate(app_setup_seq):
                # Application Setup Type (300A,0232) - Type 1
                if 'ApplicationSetupType' not in setup_item or not setup_item.ApplicationSetupType:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ApplicationSetupType (300A,0232) is required (Type 1)"
                    )

                # Application Setup Number (300A,0234) - Type 1
                if 'ApplicationSetupNumber' not in setup_item or setup_item.ApplicationSetupNumber is None:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ApplicationSetupNumber (300A,0234) is required (Type 1)"
                    )

                # Total Reference Air Kerma (300A,0250) - Type 1
                if 'TotalReferenceAirKerma' not in setup_item or setup_item.TotalReferenceAirKerma is None:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].TotalReferenceAirKerma (300A,0250) is required (Type 1)"
                    )

                # Channel Sequence (300A,0280) - Type 1
                # One or more Items shall be included in this Sequence
                if 'ChannelSequence' not in setup_item:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence (300A,0280) is required (Type 1)"
                    )
                elif len(setup_item.ChannelSequence) == 0:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence (300A,0280) must contain at least one item"
                    )
                else:
                    # Validate Channel Sequence Type 1 elements
                    channel_seq = setup_item.ChannelSequence
                    for j, channel_item in enumerate(channel_seq):
                        # Channel Number (300A,0282) - Type 1
                        if 'ChannelNumber' not in channel_item or channel_item.ChannelNumber is None:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelNumber (300A,0282) is required (Type 1)"
                            )

                        # Channel Total Time (300A,0286) - Type 1
                        if 'ChannelTotalTime' not in channel_item or channel_item.ChannelTotalTime is None:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelTotalTime (300A,0286) is required (Type 1)"
                            )

                        # Source Movement Type (300A,0288) - Type 1
                        if 'SourceMovementType' not in channel_item or not channel_item.SourceMovementType:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceMovementType (300A,0288) is required (Type 1)"
                            )

                        # Referenced Source Number (300C,000E) - Type 1
                        if 'ReferencedSourceNumber' not in channel_item or channel_item.ReferencedSourceNumber is None:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ReferencedSourceNumber (300C,000E) is required (Type 1)"
                            )

                        # Number of Control Points (300A,0110) - Type 1
                        if 'NumberOfControlPoints' not in channel_item or channel_item.NumberOfControlPoints is None:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfControlPoints (300A,0110) is required (Type 1)"
                            )

                        # Brachy Control Point Sequence (300A,02D0) - Type 1
                        if 'BrachyControlPointSequence' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].BrachyControlPointSequence (300A,02D0) is required (Type 1)"
                            )
                        elif len(channel_item.BrachyControlPointSequence) == 0:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].BrachyControlPointSequence (300A,02D0) must contain at least one item"
                            )

                        # Channel Length (300A,0284) - Type 2
                        if 'ChannelLength' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelLength (300A,0284) is required (Type 2)"
                            )

                        # Transfer Tube Number (300A,02A2) - Type 2
                        if 'TransferTubeNumber' not in channel_item:
                            result.add_error(
                                f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].TransferTubeNumber (300A,02A2) is required (Type 2)"
                            )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(RTBrachyApplicationSetupsValidator.validate_required_elements(data))

        # Always validate enumerated values
        result.merge(RTBrachyApplicationSetupsValidator.validate_enumerated_values(data))

        if config.validate_conditional_requirements:
            result.merge(RTBrachyApplicationSetupsValidator.validate_conditional_requirements(data))

        if config.validate_sequences:
            result.merge(RTBrachyApplicationSetupsValidator.validate_sequence_structures(data))

        # Additional semantic validations
        if config.validate_sequences:
            result.merge(RTBrachyApplicationSetupsValidator._validate_semantic_consistency(data))
            result.merge(RTBrachyApplicationSetupsValidator._validate_cross_references(data))

        return result

    @staticmethod
    def _validate_semantic_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate semantic consistency and logical constraints."""
        result = ValidationResult()

        # Validate uniqueness constraints
        result.merge(RTBrachyApplicationSetupsValidator._validate_uniqueness_constraints(data))

        # Validate value ranges and logical constraints
        result.merge(RTBrachyApplicationSetupsValidator._validate_value_ranges(data))

        # Validate date/time consistency
        result.merge(RTBrachyApplicationSetupsValidator._validate_datetime_consistency(data))

        return result

    @staticmethod
    def _validate_uniqueness_constraints(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate uniqueness constraints within sequences."""
        result = ValidationResult()

        # Validate Source Number uniqueness within Source Sequence
        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            source_numbers = []
            for i, source_item in enumerate(source_seq):
                if 'SourceNumber' in source_item:
                    source_number = source_item.SourceNumber
                    if source_number in source_numbers:
                        result.add_error(
                            f"SourceSequence[{i}].SourceNumber ({source_number}) must be unique within the RT Plan"
                        )
                    else:
                        source_numbers.append(source_number)

        # Validate Application Setup Number uniqueness within Application Setup Sequence
        if 'ApplicationSetupSequence' in data:
            app_setup_seq = data.ApplicationSetupSequence
            setup_numbers = []
            for i, setup_item in enumerate(app_setup_seq):
                if 'ApplicationSetupNumber' in setup_item:
                    setup_number = setup_item.ApplicationSetupNumber
                    if setup_number in setup_numbers:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ApplicationSetupNumber ({setup_number}) must be unique within the RT Plan"
                        )
                    else:
                        setup_numbers.append(setup_number)

                # Validate Channel Number uniqueness within each Application Setup
                if 'ChannelSequence' in setup_item:
                    channel_seq = setup_item.ChannelSequence
                    channel_numbers = []
                    for j, channel_item in enumerate(channel_seq):
                        if 'ChannelNumber' in channel_item:
                            channel_number = channel_item.ChannelNumber
                            if channel_number in channel_numbers:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelNumber ({channel_number}) "
                                    "must be unique within the Application Setup"
                                )
                            else:
                                channel_numbers.append(channel_number)

        return result

    @staticmethod
    def _validate_value_ranges(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate value ranges and logical constraints."""
        result = ValidationResult()

        # Validate Source Sequence value ranges
        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            for i, source_item in enumerate(source_seq):
                # Source Isotope Half Life must be positive
                if 'SourceIsotopeHalfLife' in source_item:
                    half_life = source_item.SourceIsotopeHalfLife
                    if half_life <= 0:
                        result.add_error(
                            f"SourceSequence[{i}].SourceIsotopeHalfLife ({half_life}) must be positive"
                        )

                # Reference Air Kerma Rate must be non-negative (zero for non-gamma sources)
                if 'ReferenceAirKermaRate' in source_item:
                    kerma_rate = source_item.ReferenceAirKermaRate
                    if kerma_rate < 0:
                        result.add_error(
                            f"SourceSequence[{i}].ReferenceAirKermaRate ({kerma_rate}) must be non-negative"
                        )

                # Source Encapsulation Nominal Transmission must be between 0 and 1
                if 'SourceEncapsulationNominalTransmission' in source_item:
                    transmission = source_item.SourceEncapsulationNominalTransmission
                    if transmission < 0 or transmission > 1:
                        result.add_error(
                            f"SourceSequence[{i}].SourceEncapsulationNominalTransmission ({transmission}) "
                            "must be between 0 and 1"
                        )

                # Active Source Diameter and Length must be positive if present
                if 'ActiveSourceDiameter' in source_item:
                    diameter = source_item.ActiveSourceDiameter
                    if diameter <= 0:
                        result.add_error(
                            f"SourceSequence[{i}].ActiveSourceDiameter ({diameter}) must be positive"
                        )

                if 'ActiveSourceLength' in source_item:
                    length = source_item.ActiveSourceLength
                    if length <= 0:
                        result.add_error(
                            f"SourceSequence[{i}].ActiveSourceLength ({length}) must be positive"
                        )

        # Validate Application Setup Sequence value ranges
        if 'ApplicationSetupSequence' in data:
            app_setup_seq = data.ApplicationSetupSequence
            for i, setup_item in enumerate(app_setup_seq):
                # Total Reference Air Kerma must be non-negative
                if 'TotalReferenceAirKerma' in setup_item:
                    total_kerma = setup_item.TotalReferenceAirKerma
                    if total_kerma < 0:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].TotalReferenceAirKerma ({total_kerma}) must be non-negative"
                        )

                # Validate Channel Sequence value ranges
                if 'ChannelSequence' in setup_item:
                    channel_seq = setup_item.ChannelSequence
                    for j, channel_item in enumerate(channel_seq):
                        # Channel Total Time must be positive
                        if 'ChannelTotalTime' in channel_item:
                            total_time = channel_item.ChannelTotalTime
                            if total_time <= 0:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelTotalTime ({total_time}) "
                                    "must be positive"
                                )

                        # Number of Control Points must be positive
                        if 'NumberOfControlPoints' in channel_item:
                            num_points = channel_item.NumberOfControlPoints
                            if num_points <= 0:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfControlPoints ({num_points}) "
                                    "must be positive"
                                )

                        # Channel Length must be positive if present
                        if 'ChannelLength' in channel_item:
                            length = channel_item.ChannelLength
                            if length <= 0:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelLength ({length}) "
                                    "must be positive"
                                )

        return result

    @staticmethod
    def _validate_datetime_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate date/time consistency and format."""
        result = ValidationResult()

        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            for i, source_item in enumerate(source_seq):
                # Validate Source Strength Reference Date format (YYYYMMDD)
                if 'SourceStrengthReferenceDate' in source_item:
                    date_str = source_item.SourceStrengthReferenceDate
                    try:
                        datetime.strptime(date_str, '%Y%m%d')
                    except ValueError:
                        result.add_error(
                            f"SourceSequence[{i}].SourceStrengthReferenceDate ('{date_str}') "
                            "must be in YYYYMMDD format"
                        )

                # Validate Source Strength Reference Time format (HHMMSS or HHMMSS.FFFFFF)
                if 'SourceStrengthReferenceTime' in source_item:
                    time_str = source_item.SourceStrengthReferenceTime
                    valid_time = False
                    for time_format in ['%H%M%S', '%H%M%S.%f']:
                        try:
                            datetime.strptime(time_str, time_format)
                            valid_time = True
                            break
                        except ValueError:
                            continue
                    if not valid_time:
                        result.add_error(
                            f"SourceSequence[{i}].SourceStrengthReferenceTime ('{time_str}') "
                            "must be in HHMMSS or HHMMSS.FFFFFF format"
                        )

        return result

    @staticmethod
    def _validate_cross_references(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate cross-references between sequences and elements."""
        result = ValidationResult()

        # Get available source numbers for reference validation
        available_source_numbers = set()
        if 'SourceSequence' in data:
            source_seq = data.SourceSequence
            for source_item in source_seq:
                if 'SourceNumber' in source_item:
                    available_source_numbers.add(source_item.SourceNumber)

        # Validate Referenced Source Number in Channel Sequence
        if 'ApplicationSetupSequence' in data:
            app_setup_seq = data.ApplicationSetupSequence
            for i, setup_item in enumerate(app_setup_seq):
                if 'ChannelSequence' in setup_item:
                    channel_seq = setup_item.ChannelSequence
                    for j, channel_item in enumerate(channel_seq):
                        if 'ReferencedSourceNumber' in channel_item:
                            ref_source_num = channel_item.ReferencedSourceNumber
                            if ref_source_num not in available_source_numbers:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ReferencedSourceNumber ({ref_source_num}) "
                                    f"does not reference a valid Source Number. Available: {sorted(available_source_numbers)}"
                                )

                        # Validate Number of Control Points matches actual sequence length
                        if ('NumberOfControlPoints' in channel_item and
                            'BrachyControlPointSequence' in channel_item):
                            declared_count = channel_item.NumberOfControlPoints
                            actual_count = len(channel_item.BrachyControlPointSequence)
                            if declared_count != actual_count:
                                result.add_error(
                                    f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfControlPoints ({declared_count}) "
                                    f"does not match actual BrachyControlPointSequence length ({actual_count})"
                                )

        return result
