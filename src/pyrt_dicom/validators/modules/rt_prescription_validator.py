"""RT Prescription Module DICOM validation - PS3.3 C.8.8.10.

This validator ensures compliance with DICOM standard requirements for the
RT Prescription Module, including conditional requirements, enumerated values,
and semantic consistency of dose-related attributes.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTPrescriptionValidator(BaseValidator):
    """Validator for DICOM RT Prescription Module (PS3.3 C.8.8.10)."""
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # RT Prescription Module has no Type 1 or Type 2 elements at top level
        # Required elements are within DoseReferenceSequence items
        dose_ref_seq = data.DoseReferenceSequence if 'DoseReferenceSequence' in data else []
        
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Type 1: DoseReferenceNumber is required
            if 'DoseReferenceNumber' not in dose_ref_item or dose_ref_item.DoseReferenceNumber is None:
                result.add_error(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Number (300A,0012) is required (Type 1). "
                    "This field provides a unique identification number for the dose reference."
                )
            
            # Type 1: DoseReferenceStructureType is required  
            if 'DoseReferenceStructureType' not in dose_ref_item or not dose_ref_item.DoseReferenceStructureType:
                result.add_error(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Structure Type (300A,0014) is required (Type 1). "
                    "This field specifies the structure type of the dose reference "
                    "and must be one of: POINT, VOLUME, COORDINATES, or SITE."
                )
            
            # Type 1: DoseReferenceType is required
            if 'DoseReferenceType' not in dose_ref_item or not dose_ref_item.DoseReferenceType:
                result.add_error(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Type (300A,0020) is required (Type 1). "
                    "This field specifies the type of dose reference and must be "
                    "one of: TARGET or ORGAN_AT_RISK."
                )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        dose_ref_seq = data.DoseReferenceSequence if 'DoseReferenceSequence' in data else []
        for i, dose_ref_item in enumerate(dose_ref_seq):
            structure_type = dose_ref_item.DoseReferenceStructureType if 'DoseReferenceStructureType' in dose_ref_item else ''

            # Type 1C: Referenced ROI Number required if structure type is POINT or VOLUME
            if structure_type in ['POINT', 'VOLUME']:
                if 'ReferencedROINumber' not in dose_ref_item:
                    result.add_error(
                        f"Dose Reference Sequence item {i}: "
                        f"Referenced ROI Number (3006,0084) is required when "
                        f"Dose Reference Structure Type (300A,0014) is '{structure_type}'. "
                        "This field identifies the ROI representing the dose reference "
                        "specified by ROI Number in Structure Set ROI Sequence."
                    )

            # Type 1C: Dose Reference Point Coordinates required if structure type is COORDINATES  
            if structure_type == 'COORDINATES':
                if 'DoseReferencePointCoordinates' not in dose_ref_item:
                    result.add_error(
                        f"Dose Reference Sequence item {i}: "
                        "Dose Reference Point Coordinates (300A,0018) is required when "
                        "Dose Reference Structure Type (300A,0014) is 'COORDINATES'. "
                        "This field specifies the (x,y,z) coordinates in mm in the "
                        "Patient-Based Coordinate System described in Section C.7.6.2.1.1."
                    )
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        dose_ref_seq = data.DoseReferenceSequence if 'DoseReferenceSequence' in data else []
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Dose Reference Structure Type (300A,0014)
            structure_type = dose_ref_item.DoseReferenceStructureType if 'DoseReferenceStructureType' in dose_ref_item else ''
            if structure_type:
                valid_structure_types = [stype.value for stype in DoseReferenceStructureType]
                BaseValidator.validate_enumerated_value(
                    structure_type, valid_structure_types,
                    f"Dose Reference Structure Type (300A,0014) in item {i}", result
                )
            
            # Dose Reference Type (300A,0020)
            reference_type = dose_ref_item.DoseReferenceType if 'DoseReferenceType' in dose_ref_item else ''
            if reference_type:
                valid_reference_types = [rtype.value for rtype in DoseReferenceType]
                BaseValidator.validate_enumerated_value(
                    reference_type, valid_reference_types,
                    f"Dose Reference Type (300A,0020) in item {i}", result
                )
            
            # Dose Value Purpose (300A,061D)
            value_purpose = dose_ref_item.DoseValuePurpose if 'DoseValuePurpose' in dose_ref_item else ''
            if value_purpose:
                valid_purposes = [purpose.value for purpose in DoseValuePurpose]
                BaseValidator.validate_enumerated_value(
                    value_purpose, valid_purposes,
                    f"Dose Value Purpose (300A,061D) in item {i}", result
                )
            
            # Dose Value Interpretation (300A,068B)
            value_interpretation = dose_ref_item.DoseValueInterpretation if 'DoseValueInterpretation' in dose_ref_item else ''
            if value_interpretation:
                valid_interpretations = [interp.value for interp in DoseValueInterpretation]
                BaseValidator.validate_enumerated_value(
                    value_interpretation, valid_interpretations,
                    f"Dose Value Interpretation (300A,068B) in item {i}", result
                )
        
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        dose_ref_seq = data.DoseReferenceSequence if 'DoseReferenceSequence' in data else []
        
        # Validate uniqueness of dose reference numbers (DICOM standard requirement)
        dose_ref_numbers = []
        for i, dose_ref_item in enumerate(dose_ref_seq):
            dose_ref_number = dose_ref_item.DoseReferenceNumber if 'DoseReferenceNumber' in dose_ref_item else None
            if dose_ref_number is not None:
                if dose_ref_number in dose_ref_numbers:
                    result.add_error(
                        f"Dose Reference Sequence item {i}: "
                        f"Dose Reference Number (300A,0012) value '{dose_ref_number}' must be "
                        "unique within the RT Plan in which it is created. "
                        f"This value is already used by another dose reference item."
                    )
                else:
                    dose_ref_numbers.append(dose_ref_number)
        
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Validate Dose Reference Point Coordinates format if present
            coordinates = dose_ref_item.DoseReferencePointCoordinates if 'DoseReferencePointCoordinates' in dose_ref_item else None
            if coordinates is not None:
                if not hasattr(coordinates, '__len__') or len(coordinates) != 3:
                    result.add_error(
                        f"Dose Reference Sequence item {i}: "
                        "Dose Reference Point Coordinates (300A,0018) must contain exactly "
                        "3 values representing (x,y,z) coordinates in mm in the "
                        "Patient-Based Coordinate System. "
                        f"Found {len(coordinates) if hasattr(coordinates, '__len__') else 'invalid'} values."
                    )
                else:
                    # Validate that all coordinates are numeric
                    try:
                        _ = [float(coord) for coord in coordinates]
                        # Optionally validate coordinate ranges if needed
                    except (ValueError, TypeError):
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Dose Reference Point Coordinates (300A,0018) should contain "
                            "numeric values representing coordinates in mm."
                        )

            # Validate Dose Reference UID format if present
            dose_ref_uid = dose_ref_item.DoseReferenceUID if 'DoseReferenceUID' in dose_ref_item else ''
            if dose_ref_uid:
                BaseValidator.validate_uid_format(
                    dose_ref_uid, 
                    f"Dose Reference Sequence item {i}: Dose Reference UID (300A,0013)",
                    result
                )
        
        return result
    
    @staticmethod
    def validate_dose_value_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate dose value consistency and logical constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid dose value consistency
        """
        result = ValidationResult()
        
        dose_ref_seq = data.DoseReferenceSequence if 'DoseReferenceSequence' in data else []
        for i, dose_ref_item in enumerate(dose_ref_seq):
            reference_type = dose_ref_item.DoseReferenceType if 'DoseReferenceType' in dose_ref_item else ''

            # Validate target dose values
            if reference_type == 'TARGET':
                target_min = dose_ref_item.TargetMinimumDose if 'TargetMinimumDose' in dose_ref_item else None
                target_prescription = dose_ref_item.TargetPrescriptionDose if 'TargetPrescriptionDose' in dose_ref_item else None
                target_max = dose_ref_item.TargetMaximumDose if 'TargetMaximumDose' in dose_ref_item else None
                
                # Check dose ordering: min <= prescription <= max
                if target_min is not None and target_prescription is not None:
                    if target_min > target_prescription:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Target Minimum Dose (300A,0025) should not exceed "
                            "Target Prescription Dose (300A,0026). "
                            f"Current values: minimum={target_min}Gy, prescription={target_prescription}Gy."
                        )
                
                if target_prescription is not None and target_max is not None:
                    if target_prescription > target_max:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Target Prescription Dose (300A,0026) should not exceed "
                            "Target Maximum Dose (300A,0027). "
                            f"Current values: prescription={target_prescription}Gy, maximum={target_max}Gy."
                        )
                
                if target_min is not None and target_max is not None:
                    if target_min > target_max:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Target Minimum Dose (300A,0025) should not exceed "
                            "Target Maximum Dose (300A,0027). "
                            f"Current values: minimum={target_min}Gy, maximum={target_max}Gy."
                        )
                
                # Validate underdose volume fraction
                underdose_fraction = dose_ref_item.TargetUnderdoseVolumeFraction if 'TargetUnderdoseVolumeFraction' in dose_ref_item else None
                if underdose_fraction is not None:
                    if underdose_fraction < 0.0 or underdose_fraction > 100.0:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            f"Target Underdose Volume Fraction (300A,0028) value '{underdose_fraction}' "
                            "should be between 0.0 and 100.0 percent. This field specifies the "
                            "maximum permitted fraction of the target to receive less than the "
                            "Target Prescription Dose."
                        )

            # Validate organ at risk dose values
            elif reference_type == 'ORGAN_AT_RISK':
                oar_full_volume = dose_ref_item.OrganAtRiskFullvolumeDose if 'OrganAtRiskFullvolumeDose' in dose_ref_item else None
                oar_limit = dose_ref_item.OrganAtRiskLimitDose if 'OrganAtRiskLimitDose' in dose_ref_item else None
                oar_maximum = dose_ref_item.OrganAtRiskMaximumDose if 'OrganAtRiskMaximumDose' in dose_ref_item else None
                
                # Check dose ordering for OAR
                if oar_full_volume is not None and oar_limit is not None:
                    if oar_full_volume > oar_limit:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Organ At Risk Full Volume Dose (300A,002A) should not exceed "
                            "Organ At Risk Limit Dose (300A,002B). "
                            f"Current values: full volume={oar_full_volume}Gy, limit={oar_limit}Gy."
                        )
                
                if oar_full_volume is not None and oar_maximum is not None:
                    if oar_full_volume > oar_maximum:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Organ At Risk Full Volume Dose (300A,002A) should not exceed "
                            "Organ At Risk Maximum Dose (300A,002C). "
                            f"Current values: full volume={oar_full_volume}Gy, maximum={oar_maximum}Gy."
                        )
                
                # Validate overdose volume fraction
                overdose_fraction = dose_ref_item.OrganAtRiskOverdoseVolumeFraction if 'OrganAtRiskOverdoseVolumeFraction' in dose_ref_item else None
                if overdose_fraction is not None:
                    if overdose_fraction < 0.0 or overdose_fraction > 100.0:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            f"Organ At Risk Overdose Volume Fraction (300A,002D) value '{overdose_fraction}' "
                            "should be between 0.0 and 100.0 percent. This field specifies the "
                            "maximum permitted fraction of the organ at risk to receive more than "
                            "the Organ At Risk Maximum Dose."
                        )

            # Validate general dose values
            delivery_warning = dose_ref_item.DeliveryWarningDose if 'DeliveryWarningDose' in dose_ref_item else None
            delivery_maximum = dose_ref_item.DeliveryMaximumDose if 'DeliveryMaximumDose' in dose_ref_item else None

            if delivery_warning is not None and delivery_maximum is not None:
                if delivery_warning > delivery_maximum:
                    result.add_warning(
                        f"Dose Reference Sequence item {i}: "
                        "Delivery Warning Dose (300A,0022) should not exceed "
                        "Delivery Maximum Dose (300A,0023). "
                        f"Current values: warning={delivery_warning}Gy, maximum={delivery_maximum}Gy."
                    )

            # Validate constraint weight
            constraint_weight = dose_ref_item.ConstraintWeight if 'ConstraintWeight' in dose_ref_item else None
            if constraint_weight is not None and constraint_weight < 0:
                result.add_warning(
                    f"Dose Reference Sequence item {i}: "
                    f"Constraint Weight (300A,0021) value '{constraint_weight}' should be "
                    "non-negative. This field represents the relative importance of "
                    "satisfying the constraint, where higher values indicate more important constraints."
                )

            # Validate nominal prior dose
            nominal_prior = dose_ref_item.NominalPriorDose if 'NominalPriorDose' in dose_ref_item else None
            if nominal_prior is not None and nominal_prior < 0:
                result.add_warning(
                    f"Dose Reference Sequence item {i}: "
                    f"Nominal Prior Dose (300A,001A) value '{nominal_prior}' should be "
                    "non-negative. This field specifies the dose in Gy from prior "
                    "treatment to this dose reference (e.g., from a previous course of treatment)."
                )
        
        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(RTPrescriptionValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(RTPrescriptionValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(RTPrescriptionValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(RTPrescriptionValidator.validate_sequence_structures(data))
        
        # Always validate dose value consistency
        result.merge(RTPrescriptionValidator.validate_dose_value_consistency(data))
        
        return result
    
