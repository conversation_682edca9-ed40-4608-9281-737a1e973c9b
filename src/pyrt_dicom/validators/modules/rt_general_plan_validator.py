"""RT General Plan Module DICOM validation - PS3.3 C.8.8.9

Validates RT General Plan Module requirements according to DICOM PS3.3 C.8.8.9.
Provides comprehensive validation of Type 1, Type 2, Type 3, and conditional requirements
with clear, actionable error messages for end users.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import PlanIntent, RTPlanGeometry, RTPlanRelationship

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTGeneralPlanValidator(BaseValidator):
    """Validator for DICOM RT General Plan Module (PS3.3 C.8.8.9).

    Validates all requirements from DICOM PS3.3 C.8.8.9 including:
    - Type 1 required elements (RT Plan Label, RT Plan Geometry)
    - Type 2 elements with empty value handling (RT Plan Date, RT Plan Time)
    - Type 1C conditional requirements (Referenced Structure Set Sequence)
    - Enumerated value validation for all coded elements
    - Sequence structure validation with detailed error reporting
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Type 1: RT Plan Label (300A,0002)
        if 'RTPlanLabel' not in data:
            result.add_error(
                "RT Plan Label (300A,0002) is required (Type 1). "
                "Provide a user-defined label for the treatment plan."
            )
        elif not data.RTPlanLabel:
            result.add_error(
                "RT Plan Label (300A,0002) cannot be empty (Type 1). "
                "Provide a meaningful label for the treatment plan."
            )

        # Type 1: RT Plan Geometry (300A,000C)
        if 'RTPlanGeometry' not in data:
            result.add_error(
                "RT Plan Geometry (300A,000C) is required (Type 1). "
                "Specify whether the plan is based on PATIENT or TREATMENT_DEVICE geometry."
            )
        elif not data.RTPlanGeometry:
            result.add_error(
                "RT Plan Geometry (300A,000C) cannot be empty (Type 1). "
                "Must be either 'PATIENT' or 'TREATMENT_DEVICE'."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Type 1C: Referenced Structure Set Sequence required if RT Plan Geometry is PATIENT
        if 'RTPlanGeometry' in data:
            rt_plan_geometry = data.RTPlanGeometry
            if rt_plan_geometry == "PATIENT":
                if 'ReferencedStructureSetSequence' not in data:
                    result.add_error(
                        "Referenced Structure Set Sequence (300C,0060) is required when "
                        "RT Plan Geometry (300A,000C) is PATIENT (Type 1C). "
                        "The RT Structure Set on which the RT Plan is based must be specified. "
                        "See DICOM PS3.3 C.8.8.9.1 for details."
                    )
                elif not data.ReferencedStructureSetSequence:
                    result.add_error(
                        "Referenced Structure Set Sequence (300C,0060) cannot be empty when "
                        "RT Plan Geometry (300A,000C) is PATIENT (Type 1C). "
                        "At least one structure set reference is required."
                    )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # RT Plan Geometry (300A,000C) - Type 1
        if 'RTPlanGeometry' in data:
            rt_plan_geometry = data.RTPlanGeometry
            if rt_plan_geometry:
                valid_geometries = [geometry.value for geometry in RTPlanGeometry]
                if rt_plan_geometry not in valid_geometries:
                    result.add_error(
                        f"RT Plan Geometry (300A,000C) value '{rt_plan_geometry}' is invalid. "
                        f"Must be one of: {', '.join(valid_geometries)}. "
                        "PATIENT indicates RT Structure Set exists, TREATMENT_DEVICE indicates no patient geometry."
                    )

        # Plan Intent (300A,000A) - Type 3
        if 'PlanIntent' in data:
            plan_intent = data.PlanIntent
            if plan_intent:
                valid_intents = [intent.value for intent in PlanIntent]
                if plan_intent not in valid_intents:
                    result.add_error(
                        f"Plan Intent (300A,000A) value '{plan_intent}' is invalid. "
                        f"Must be one of: {', '.join(valid_intents)}. "
                        "Specify the clinical intent of this treatment plan."
                    )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Referenced Structure Set Sequence - each item needs SOP Class and Instance UIDs
        if 'ReferencedStructureSetSequence' in data:
            ref_structure_set_seq = data.ReferencedStructureSetSequence
            for i, item in enumerate(ref_structure_set_seq):
                if not item.get('ReferencedSOPClassUID'):
                    result.add_error(
                        f"Referenced Structure Set Sequence item {i+1}: "
                        "Referenced SOP Class UID (0008,1150) is required. "
                        "Specify the SOP Class UID of the referenced RT Structure Set."
                    )
                if not item.get('ReferencedSOPInstanceUID'):
                    result.add_error(
                        f"Referenced Structure Set Sequence item {i+1}: "
                        "Referenced SOP Instance UID (0008,1155) is required. "
                        "Specify the SOP Instance UID of the referenced RT Structure Set."
                    )

        # Referenced RT Plan Sequence - each item needs SOP references and relationship
        if 'ReferencedRTPlanSequence' in data:
            ref_rt_plan_seq = data.ReferencedRTPlanSequence
            for i, item in enumerate(ref_rt_plan_seq):
                if not item.get('ReferencedSOPClassUID'):
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i+1}: "
                        "Referenced SOP Class UID (0008,1150) is required. "
                        "Specify the SOP Class UID of the referenced RT Plan."
                    )
                if not item.get('ReferencedSOPInstanceUID'):
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i+1}: "
                        "Referenced SOP Instance UID (0008,1155) is required. "
                        "Specify the SOP Instance UID of the referenced RT Plan."
                    )

                # Validate RT Plan Relationship enumerated values
                rt_plan_relationship = item.get('RTPlanRelationship', '')
                if not rt_plan_relationship:
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i+1}: "
                        "RT Plan Relationship (300A,0055) is required. "
                        "Specify the relationship of the referenced plan to the current plan."
                    )
                else:
                    valid_relationships = [rel.value for rel in RTPlanRelationship]
                    if rt_plan_relationship not in valid_relationships:
                        result.add_error(
                            f"Referenced RT Plan Sequence item {i+1}: "
                            f"RT Plan Relationship (300A,0055) value '{rt_plan_relationship}' is invalid. "
                            f"Must be one of: {', '.join(valid_relationships)}."
                        )

        # Treatment Site Code Sequence - each item needs code value, scheme, and meaning
        if 'TreatmentSiteCodeSequence' in data:
            treatment_site_code_seq = data.TreatmentSiteCodeSequence
            for i, item in enumerate(treatment_site_code_seq):
                if not item.get('CodeValue'):
                    result.add_error(
                        f"Treatment Site Code Sequence item {i+1}: "
                        "Code Value (0008,0100) is required. "
                        "Provide the anatomical region code value (e.g., from SNOMED CT)."
                    )
                if not item.get('CodingSchemeDesignator'):
                    result.add_error(
                        f"Treatment Site Code Sequence item {i+1}: "
                        "Coding Scheme Designator (0008,0102) is required. "
                        "Specify the coding scheme (e.g., 'SCT' for SNOMED CT)."
                    )
                if not item.get('CodeMeaning'):
                    result.add_error(
                        f"Treatment Site Code Sequence item {i+1}: "
                        "Code Meaning (0008,0104) is required. "
                        "Provide human-readable description of the anatomical site."
                    )

        # Referenced Dose Sequence - each item needs SOP Class and Instance UIDs
        if 'ReferencedDoseSequence' in data:
            ref_dose_seq = data.ReferencedDoseSequence
            for i, item in enumerate(ref_dose_seq):
                if not item.get('ReferencedSOPClassUID'):
                    result.add_error(
                        f"Referenced Dose Sequence item {i+1}: "
                        "Referenced SOP Class UID (0008,1150) is required. "
                        "Specify the SOP Class UID of the referenced RT Dose object."
                    )
                if not item.get('ReferencedSOPInstanceUID'):
                    result.add_error(
                        f"Referenced Dose Sequence item {i+1}: "
                        "Referenced SOP Instance UID (0008,1155) is required. "
                        "Specify the SOP Instance UID of the referenced RT Dose object."
                    )

        # RT Assertions Sequence validation (basic structure check)
        # Note: RT Assertions Sequence (0044,0110) doesn't have a standard keyword, so we check by tag
        if hasattr(data, 'get') and data.get((0x0044, 0x0110)) is not None:
            rt_assertions_seq = data.get((0x0044, 0x0110))
            for i, item in enumerate(rt_assertions_seq):
                # Basic validation - specific assertion requirements would depend on the assertion type
                if not any(key in item for key in ['AssertionCodeSequence', 'AssertionUID']):
                    result.add_warning(
                        f"RT Assertions Sequence item {i+1}: "
                        "Should contain assertion identification elements (Assertion Code Sequence or Assertion UID). "
                        "See DICOM PS3.3 Table 10.30-1 Assertion Macro for details."
                    )
        elif hasattr(data, '__contains__') and (0x0044, 0x0110) in data:
            # Handle BaseModule case where we can access by tag
            rt_assertions_seq = getattr(data, 'RTAssertionsSequence', None)
            if rt_assertions_seq:
                for i, item in enumerate(rt_assertions_seq):
                    # Basic validation - specific assertion requirements would depend on the assertion type
                    if not any(key in item for key in ['AssertionCodeSequence', 'AssertionUID']):
                        result.add_warning(
                            f"RT Assertions Sequence item {i+1}: "
                            "Should contain assertion identification elements (Assertion Code Sequence or Assertion UID). "
                            "See DICOM PS3.3 Table 10.30-1 Assertion Macro for details."
                        )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(RTGeneralPlanValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(RTGeneralPlanValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(RTGeneralPlanValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(RTGeneralPlanValidator.validate_sequence_structures(data))

        return result
